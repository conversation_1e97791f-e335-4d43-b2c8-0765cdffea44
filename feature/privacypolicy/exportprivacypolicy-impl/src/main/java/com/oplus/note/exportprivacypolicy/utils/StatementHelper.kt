/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - PrivacyPolicyTool.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/6/4
 * * Author: StatementHelper
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 **************************************************************************/
package com.oplus.note.exportprivacypolicy.utils

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.View
import com.coui.appcompat.statement.COUIStatementClickableSpan

object StatementHelper {
    @JvmStatic
    fun createSpannableString(context: Context, fullText: String, map: Map<String, OnSpanClickListener>): SpannableStringBuilder {
        return SpannableStringBuilder(fullText).apply {
            map.forEach { (link, listener) ->
                val startIndex = fullText.indexOf(link)
                val endIndex = startIndex + link.length
                setSpan(object : COUIStatementClickableSpan(context) {
                    override fun onClick(widget: View) {
                        super.onClick(widget)
                        listener.onSpanClick()
                    }
                }, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
    }

    interface OnSpanClickListener {
        fun onSpanClick()
    }
}