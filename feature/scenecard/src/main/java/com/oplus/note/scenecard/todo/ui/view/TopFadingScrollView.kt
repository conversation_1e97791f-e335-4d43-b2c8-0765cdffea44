/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - FadingScrollView
 ** Description:
 **         v1.0:   Scrollview with top fade effect removed
 **
 ** Version: 1.0
 ** Date: 2023/02/14
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/14   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.util.AttributeSet
import android.widget.ScrollView

class TopFadingScrollView(context: Context, attr: AttributeSet) :
    ScrollView(context, attr) {

    override fun getBottomFadingEdgeStrength(): Float {
        return 0f
    }
}