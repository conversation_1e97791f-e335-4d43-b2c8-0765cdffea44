/*********************************************************************************
 ** Copyright (C), 2008-2014, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - COUISlideDeleteAnimation.java
 ** Description:
 **     A abstract class for animation in card of note.
 **
 ** Version: 1.0
 ** Date: 2023-3-14
 ** Author: 80374121
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <data>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80374121                 2023-3-14   1.0         Annotate this module
 ********************************************************************************/

package com.oplus.note.scenecard.anim;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.LayoutDirection;
import android.view.View;
import com.coui.appcompat.animation.COUIMoveEaseInterpolator;
import com.coui.appcompat.slideview.COUISlideView;
import java.util.Locale;

/**
 * A abstract class that offers a set of animations in construction for COUISlideView.
 *
 * <p>It offers two animations which are played together. One of animations is to change the
 * height of{@link #mSlideView}, and another is to scroll.</p>
 * <AUTHOR>
 */
public abstract class CardDeleteAnimation {

    private static final int ITEM_VIEW_DURATION = 500;
    private static final int ANIMATION_DELAY = 10;

    private View mSlideView;
    private ObjectAnimator mItemViewAnimator;
    private AnimatorSet mAnimatorSet;
    /**
     * This constructor is used when a COUISlideDeleteAnimation is created.
     *
     * @param slideView the view will be deleted
     * @param itemView a {@link COUISlideView}
     * @param startX the final height of {@link #mSlideView}
     * @param targetX the final height of {@link #mSlideView}
     */
    @SuppressLint("ObjectAnimatorBinding")
    public CardDeleteAnimation(View itemView, View slideView, int startX, int targetX) {
        mSlideView = slideView;

        boolean isRtl = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL;
        // alpha 动画通过 item 的 remove 动画触发，不用再写一个
        mItemViewAnimator = ObjectAnimator.ofFloat(itemView, "translationX", 0,
                isRtl ? slideView.getMeasuredWidth() : -slideView.getMeasuredWidth());
        mItemViewAnimator.setInterpolator(new COUIMoveEaseInterpolator());
        mItemViewAnimator.setDuration(ITEM_VIEW_DURATION);
        mItemViewAnimator.setStartDelay(ANIMATION_DELAY);
        mItemViewAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {
            }
        });

        mAnimatorSet = new AnimatorSet();
        mAnimatorSet.play(mItemViewAnimator);

    }

    /**
     * Start the animation.
     */
    public void startAnimation() {
        itemViewDelete();
        mAnimatorSet.start();
    }

    /**
     * A abstract method which is intend to delete target item.
     */
    public abstract void itemViewDelete();
}
