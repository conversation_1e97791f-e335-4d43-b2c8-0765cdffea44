/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : AttrMenuData.kt
 * Description    : 附件菜单操作需要的数据类，必须和web端定义一致。图片、卡片、文字替换等操作
 * Version        : 1.0
 * Date           : 2024/1/11
 * Author         : wangyinglei
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wangyinglei   2024/1/11         1.0           create
 */
package com.oplus.notes.webviewcoverpaint.container.api

import androidx.annotation.Keep
import com.oplus.note.repo.note.entity.CardAttr

@Keep
data class AttrMenuData(
    val clickAttachId: String = "",
    val text: String = "",
    val card: CardAttr? = null,
    val image: com.oplus.notes.webviewcoverpaint.container.api.ImageInfo? = null
)