/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : IWebViewProxyCache.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/3/29
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/3/29        1.0           create
 */
package com.oplus.notes.webviewcoverpaint.cache.api

import android.content.Context
import androidx.annotation.StyleRes
import com.oplus.note.osdk.adapter.SystemPropertiesAdapter

interface IWebViewProxyCache {
    companion object {
        @JvmStatic
        val ENABLE_CACHE = false

        @JvmStatic
        val FORCE_USE_SYSTEM_WEB_VIEW = SystemPropertiesAdapter.getBoolean("debug.ForceUseSystemWebView", false)

        @JvmStatic
        val MAX_TABLE_ROW_COL_COUNT = SystemPropertiesAdapter.getInt("debug.MaxTableRowColCount", 0)

        const val CACHE_SIZE_IN_TWO_PANEL = 3
    }

    fun createWebViewProxyCache(context: Context, @StyleRes themeResId: Int, cacheSize: Int): Boolean
    fun updateCacheSize(cacheSize: Int)
    fun acquireWebViewProxy(context: Context, @StyleRes themeResId: Int): Any
    fun recycleWebViewProxy(webViewProxy: Any)
    fun tryRecycleAllCachedWebViewProxy(context: Context, force: Boolean = false)

    fun addWebViewForceDestroyListener(listener: WebViewForceDestroyListener)

    fun removeWebViewForceDestroyListener(listener: WebViewForceDestroyListener)
}

interface WebViewForceDestroyListener {
    fun onWebViewDestroyed()
}