import {Editor, isNodeSelection, type Range} from '@tiptap/core'
import * as ActiveObserver from '@ts/TipTapAttrsObserver'
import { Attrs, DOMSerializer, Node } from '@tiptap/pm/model'
import type {
    Coordinate,
    InputContent,
    CardAttr,
    Rect,
    Property,
    ImageInfo,
    RecordAttr,
    FileCardData,
    PaintAttr,
    UpdatePaintAttr,
    AttrMenuData,
    AlignOption,
    CombinedCardCheck,
    BasicCssParams,
    SkinCssParams,
    BorderImage,
    FocusedEditor,
    FocusInfo,
    TitleInfo,
    SearchMatchedRange,
    CaptureElementInfo,
    MoveAttachInfo,
    AIGCGenerateMargin,
    AIGCResult,
    EditorRang,
    ScrollToBottomInfo,
    UpdateElementAttrs,
    InsertParas,
    AIGCSelectionValue,
    PasteResult,
    VideoCardAttr,
    UpdateAttachmentAttr
} from '@ts/Data'
import domtoimage from 'dom-to-image'
import {
    compositeColors,
    hexToRgba,
    isUnitPx, isUnitRem,
    pxToRem,
    remToPxWithRemRadio,
} from './utils/Convert'
import { getCheckBoxPngUrl, getCheckSvgUrl, getLinkMarkerSvgUrl, getUnCheckSvgUrl } from "@ts/utils/CheckBoxSvgHelper"
import { dataTransform, tiptapHtml2NoteHtml, replaceEscapeCharaterToEntity } from "@ts/html/HtmlHelper"
import {
    AttachmentAttr,
    AttachData,
    ContactCardAttr,
    ContentInfo, NoteHtmlData,
    PageResult,
    PictureAttr,
    ScheduleCardAttr
} from "@ts/Data"
import * as HtmlHelper from "@ts/html/HtmlHelper"
import { AttrStep } from '@tiptap/pm/transform'
import * as Log from '@ts/utils/Logger'
import { initThemeIcons } from './SVGResource'
import { ATTR_MODIFY_TITLE } from './custom/history/ObserveTitleHistoryExtension'
import { Constants } from './utils/Constants'
import html2canvas from 'html2canvas'
import StringBuilder from './utils/StringBuilder'
import { invoke, until,useWindowSize, watchImmediate } from '@vueuse/core'
import { RichTextToolsConstant } from './richtext/RichTextToolsConstant'
import { joinListBackwards, joinListForwards } from './custom/commands'
import {NodeSelection, TextSelection} from '@tiptap/pm/state'
import {findParentNode, getNodeType} from '@tiptap/vue-3'
import {
    isBlockquoteNode, isCodeBlockNode, isHardBreakNode,
    isListNode, isHeadingNode as isHeadingNodeInHelper,
    isParagrahNode,
    isTableNode,
    isHeadingNode,
    queryTiptapContentElement,
    queryTiptapContentParentElement,
    getTextBetween2,
    queryTableRowDotElement,
    queryTableColumnDotElement,
    isSelectionInTable2,
} from './custom/helpers'
import {isAttachNode, isLinksCardNode, isListType, isSummaryStreamTipNode, MARK_NAME_TEXT_STYLE} from './custom/helpers'
import { EditorView } from '@tiptap/pm/view'
import { FILE_CARD_NODE_NAME } from './custom/FileCard'
import { SCHEDULE_CARD_NODE_NAME } from './custom/ScheduleCard'
import { CONTACT_CARD_NODE_NAME } from './custom/ContactCard'
import { RECORD_NODE_NAME } from './custom/record/Record'
import { serializerContentToPlainText } from './custom/clipboard/plainTextSerializer'
import { isAttachElementFocus, afterClass, beforeClass, getCursorInfo, removeAllBlockSelected, getReferenceCursorElementByAnyone } from '@ts/utils/attachCursor'
import { PickedInputContent, removeSummaryStreamTipBackgroundStyle, SUMMARY_STREAM_TIPS_NODE_NAME } from './custom/SummaryStreamTip'
import { attachKeyMapEvent, getIndexByElement } from '@ts/utils/AttachKeyMapEvent'
import { pasteClipboardData } from './custom/clipboard/RichTextPasteHelper'
import type { EditorState  } from "@tiptap/pm/state"
import * as Data from "@ts/Data";

import { getColumnDotSvgUrl, getLeftColHandleSvg, getRowDotSvgUrl, getTopRowHandleSvg } from './custom/table/helpers/TableSvgHelper'
import { removeActiveStyle } from './custom/table/utilities/TouchHandle';
import { isCellSelection } from './custom/table/utilities/isCellSelection';
import { updateDotMenuVisibility } from './custom/table/utilities/updateDotMenuVisibility';
import { ScrollHelper } from './custom/table/helpers/ScrollHelper';
import { NAME_SHOW_COLUMN_DOT_MENU_SELECTED, NAME_SHOW_ROW_DOT_MENU, NAME_SHOW_ROW_DOT_MENU_SELECTED } from './custom/table/TableView'
import { removeSelectionCellBorderWidget } from "@ts/custom/table/helpers/selectionCellLighlight";
import { isInTable } from '@tiptap/pm/tables'
import { queryTiptapContainerElement } from '@ts/custom/helpers/documentHelper'
import { setBottomPlaceHolderHeight } from './utils/CommonUtil'
import { CursorSelection, isCursorSelection } from '@/ts/CustomSelection/CursorSelection'

export const DEBUG_ON_WEB = import.meta.env.DEV

export function textClick(pos: number, fromEditor: number) {
    console.log(`textClick: pos=${pos}, fromEditor=${fromEditor}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onTextClick(pos, fromEditor)
    }
}

export function imageClick(src: string) {
    console.log(`imageClick: src = ${src}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onImageClick(src)
    }
}
export function videoClick(src: string, duration: String) {
    console.log(`videoClick: src = ${src} duration = ${duration}`)
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onVideoClick(src, duration)
    }
}

export function fileCardClick(src: string) {
    console.log(`fileCardClick: src = ${src}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onDocumentFileClick(src)
    }
}

export function cardClick(src: string) {
    console.log(`cardClick: src = ${src}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onCardClick(src)
    }
}

export function taskClick(clicked: string) {
    console.log(`taskClick: clicked = ${clicked}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onTaskClick(clicked)
    }
}
export function linkClick(href: string, eventX: number, eventY: number, pos: number) {
    console.log(`linkClick: href = ${href}`);
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onLinkClick(href, eventX, eventY, pos)
    }
}

export function imageDoubleClick(src: string) {
    console.log('imageDoubleClick:');
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onImageDoubleClick(src)
    }
}

export function useRegisterCustomHandler(titleEditor: Editor, contentEditor: Editor) {
    // native是否存在某方法
    // (window as any).WebViewJavascriptBridge.hasNativeMethod('callJavaFromJs', function (responseData: any) {
    //   console.log(`hasNativeMethod[callJavaFromJs]:${responseData}`)
    // });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = setText(titleEditor, contentEditor, data as string)
        responseCallback(result ? "success" : "fail")
    });


    //原本在android设置webview的margin，为了让webview全屏显示，改为设置webview的 padding。在原有padding基础上再加上这个margin值
    (window as any).WebViewJavascriptBridge.registerHandler('updateContentMargin', function (data: any, responseCallback: (arg0: string) => void) {
        const marginValue = Number(data)
        // 确保 margin 值不为负数，避免布局异常
        const safePadding = Math.max(0, marginValue)
        document.getElementById('app').style.paddingLeft = safePadding + 'px'
        document.getElementById('app').style.paddingRight = safePadding + 'px'
        responseCallback("done")
        console.log(`updateContentMargin original=${data} safe=${safePadding} paddingLeft=${document.getElementById('app').style.paddingLeft}`)
    });

    //是否无涂鸦，进入涂鸦模式也算有涂鸦
    (window as any).WebViewJavascriptBridge.registerHandler('callSetPaintEmpty', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callSetPaintEmpty isEmpty=${data} before contentPaddingLeft=${ActiveObserver.instance.contentPaddingLeft.value}`)
        const oldIsPaintEmpty = ActiveObserver.instance.isPaintEmpty.value
        if (data === 'true') {
            //无涂鸦，使用px，避免内容和标题的左右边距太大
            ActiveObserver.instance.isPaintEmpty.value = true
            if (isUnitRem(ActiveObserver.instance.contentPaddingLeft.value)) {
                ActiveObserver.instance.contentPaddingLeft.value = remToPxWithRemRadio(ActiveObserver.instance.contentPaddingLeft.value, 16) +'px'
                ActiveObserver.instance.contentPaddingRight.value = remToPxWithRemRadio(ActiveObserver.instance.contentPaddingRight.value, 16) +'px'
                ActiveObserver.instance.titlePaddingLeft.value = remToPxWithRemRadio(ActiveObserver.instance.titlePaddingLeft.value, 16) +'px'
                ActiveObserver.instance.titlePaddingRight.value = remToPxWithRemRadio(ActiveObserver.instance.titlePaddingRight.value, 16) +'px'
            }
        } else {
            //有涂鸦，使用 rem，避免涂鸦错位
            ActiveObserver.instance.isPaintEmpty.value = false
            if (isUnitPx(ActiveObserver.instance.contentPaddingLeft.value)) {
                ActiveObserver.instance.contentPaddingLeft.value = pxToRem(ActiveObserver.instance.contentPaddingLeft.value)
                ActiveObserver.instance.contentPaddingRight.value = pxToRem(ActiveObserver.instance.contentPaddingRight.value)
                ActiveObserver.instance.titlePaddingLeft.value = pxToRem(ActiveObserver.instance.titlePaddingLeft.value)
                ActiveObserver.instance.titlePaddingRight.value = pxToRem(ActiveObserver.instance.titlePaddingRight.value)
            }
        }
        if (oldIsPaintEmpty != ActiveObserver.instance.isPaintEmpty.value) {
            contentEditor.commands.updateImageWidthHeight()
        }
        console.log(`callSetPaintEmpty after contentPaddingLeft=${ActiveObserver.instance.contentPaddingLeft.value}`)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callChangeWebViewWidthScaleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callChangeWebViewWidthScaleFromJava data=${data}`)
        const tiptapContentParent = queryTiptapContentParentElement()
        if (String(data) === 'true') {
            tiptapContentParent.style.setProperty('overflow-x', 'hidden')
        } else {
            tiptapContentParent.style.removeProperty('overflow-x')
        }
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callClearContentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        clearContent(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertImageFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertImage(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateImageFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateImage(contentEditor, data as string)
        responseCallback("done")

    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetTableFullDisplayWidthFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callGetTableFullDisplayWidthFromJava ${data}`)
        const isGenerate = String(data) === 'true'
        ActiveObserver.instance.isGenerateImage = isGenerate
        // true 图片开始生成
        if(isGenerate) {
            // 获取最大表格元素的宽度
            const width = getMaxClientTableWidth()
            // 宽度是否超出屏幕宽度
            const isExceed = width > innerWidth
            // 屏幕超出的时候 要将表格单元格(单元格宽度是响应式的)和卡片宽度固定
            isExceed && fixWidthWhenImageShare()
            // 隐藏表格的Cell的选中态
            removeAllBlockSelected()
            console.log('callGetTable-FullDisplayWidth-FromJava-response-callback', {width, isExceed})
            responseCallback(String(JSON.stringify({width, isExceed})))
        } else {
            // 图片生成结束 移除添加的样式
            restoreWidthImageShareFinish()
            responseCallback('true')
        }
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertVideoPlaceHolderFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertVideoCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateVideoFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateVideoCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callUpdateCardFromJava: in`)
        updateCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callReplaceNodeByCardFromJava: in`)
        replaceNodeByCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByImageFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callReplaceNodeByImageFromJava: in`)
        replaceNodeByImage(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceAttachmentByTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        replaceAttachmentByText(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByPaintFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        replaceNodeByPaint(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByDocumentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        replaceNodeByDocument(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByVideoFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        replaceNodeByVideo(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callReplaceNodeByAudioFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        replaceNodeByAudio(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callGetHasLinkCard', function (data: any, responseCallback: (arg0: string) => void) {
        try {
            const has = hasLinkCard(contentEditor)
            console.log(`bxx linkCard:${has}`)
            responseCallback(JSON.stringify(has))
        } catch (e) {
            console.log(`callGetHasLinkCard error ${e}`)
            responseCallback(JSON.stringify(false))
        }
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetTextAndHtmlFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const fetchTextAndHtml = () => {
            const textData = getText(contentEditor)
             //getHTML接口，会自动将编辑器中的 & < > 这几个字符进行转义，其他特殊字符验证会直接返回源字符，不会进行转义
            const titleHtml = getHTML(titleEditor)
            const contentHtml = getHTML(contentEditor)
            return { textData, titleHtml, contentHtml }
        }
        const processData = () => {
            const { textData, titleHtml, contentHtml } = fetchTextAndHtml()
            const html = tiptapHtml2NoteHtml(titleHtml, contentHtml)
            const noteData = { text: textData, htmlData: html }
            responseCallback(JSON.stringify(noteData))
        }
        try {
            processData()
        } catch (e) {
            console.log(`callGetHTMLFromJava error ${e}`)
            try {
                // 出异常，第二次获取数据
                processData()
            } catch (e) {
                // 如果第二次获取数据出异常则返回错误
                const errorData = {bridgeName:`callGetTextAndHtmlFromJava`, errorMessage: `${e}`}
                responseCallback(JSON.stringify(errorData))
            }
        }
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetRecordDetailRectFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const rect = getRecordDetailRect(contentEditor, data as string)
        responseCallback(rect)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetSelectTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = getSelectText(titleEditor, contentEditor)
        responseCallback(result)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetRecordCardRectFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const rect = getRecordCardRect(contentEditor, data as string)
        responseCallback(rect)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callClearFocusFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        clearFocus(titleEditor, contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callFocusFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        focus(titleEditor, contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callStartSpeechRecognizeFromJava', function (data: any, responseCallback: (arg0: string) => void) {

        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetCursorStartPosition', function (data: any, responseCallback: (arg0: string) => void) {
        const result = getCursorPosition(contentEditor)
        responseCallback(JSON.stringify(result))
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetFocusedEditorFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const editor = getFocusedEditor(titleEditor, contentEditor)
        if (editor.isContentEditor) {
            const from = editor.editor.state.selection.from
            const to = editor.editor.state.selection.to

            const node = editor.editor.state.doc.resolve(to).node(1)
            const isAttach = !node || isAttachNode(node)
            responseCallback(JSON.stringify(isAttach ? to + 1 : to))
            console.log(`GetFocusedEditor from ${from},to ${to},response value: ${isAttach ? to + 1 : to}`)
        } else {
            console.log(`GetFocusedEditor editor ${editor}`)
            responseCallback("-1")
        }
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetBoldFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        if (data === "true") {
            setBold(contentEditor)
            responseCallback("done")
        } else if (data === "false") {
            unsetBold(contentEditor)
            responseCallback("done")
        }
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetTitleStyleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setTitleStyle(contentEditor,data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetBlockQuoteFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const setValue = (data as string == "true") ? true : false
        setBlockQuote(contentEditor, setValue)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetContentStyleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setContentStyle(contentEditor,data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetTextColorFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const { editor, isTitleEditor, isContentEditor } = getFocusedEditor(titleEditor, contentEditor)
        console.log(`setTextColor: isTitleEditor=${isTitleEditor}, isContentEditor=${isContentEditor}`)
        setTextColor(editor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetItalicFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        if (data === "true") {
            setItalic(contentEditor)
            responseCallback("done")
        } else if (data === "false") {
            unsetItalic(contentEditor)
            responseCallback("done")
        }
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetUnderlineFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        if (data === "true") {
            setUnderline(contentEditor)
            responseCallback("done")
        } else if (data === "false") {
            unsetUnderline(contentEditor)
            responseCallback("done")
        }
    });

     (window as any).WebViewJavascriptBridge.registerHandler('callSetStrikethroughFromJava', function (data: any, responseCallback: (arg0: string) => void) {
            if (data === "true") {
                setStrikethrough(contentEditor)
                responseCallback("done")
            } else if (data === "false") {
                unsetStrikethrough(contentEditor)
                responseCallback("done")
            }
        });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetFontSizeFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setFontSize(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetRecordCurrentTimeFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setRecordCurrentTime(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callToggleBulletListFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        toggleBulletList(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callToggleOrderedListFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        toggleOrderedList(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callToggleBulletListHXFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        toggleBulletListHX(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callToggleTaskListFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        toggleTaskList(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetTextAlignFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const { editor, isTitleEditor, isContentEditor } = getFocusedEditor(titleEditor, contentEditor)
        console.log(`setTextAlign: ${data}, isTitleEditor=${isTitleEditor}, isContentEditor=${isContentEditor}`)
        setTextAlign(editor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callFocusCoordinateFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        focusCoordinate(titleEditor, contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callScrollIntoViewFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const { editor } = getFocusedEditor(titleEditor, contentEditor)
        if (editor) {
            editor.commands.scrollIntoView()
        }
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetNodeRectByCoordsFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const rect = getNodeRectByCoords(contentEditor, data as string)
        responseCallback(rect)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetNodeRectBySrcFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const rect = getImageRectBySrc(contentEditor, data as string)
        responseCallback(rect)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUndoFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const { selection } = contentEditor.state
        /**
         * 光标在附件两侧回退, 文档会从编辑态切换到浏览态
         * 如果当前选区是CursorSelection, 就先将光标切换到之前的位置
         * 然后再执行回退操作
        */
        if(isCursorSelection(selection) && (window as any).injectedObject && (window as any).injectedObject.isImeVisible()) {
            // 光标再附件两侧,并且有键盘
            contentEditor.commands.focusNoScroll((selection as CursorSelection).preFrom)
            const style = document.head.appendChild(document.createElement('style'))
            // 避免光标切换的过程中 出现光标切换的现象, 先设置成透明色
            style.innerHTML = `*{ caret-color: transparent !important; }`
            setTimeout(() => {
                contentEditor.chain().customUndo().run()
                contentEditor.commands.updateNativeMarksStatus()
                style.remove()
            });
        } else {
            contentEditor.chain().customUndo().run()
            contentEditor.commands.updateNativeMarksStatus()
        }
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callRedoFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        contentEditor.chain().customRedo().run()
        contentEditor.commands.updateNativeMarksStatus()
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertContactCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertContactCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertScheduleCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertScheduleCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertRecordCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertRecordCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertFileCardFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertFileCard(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateFileDocThumbnailFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateFileDocThumbnail(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateFileNameFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateFileName(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetTextSelectionFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setTextSelection(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetTextSelectionAllFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setTextSelectionAll(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callCancelTextSelectionAllFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        cancelTextSelectionAll(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetBackgroundColorFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setBackgroundColor(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUnsetBackgroundColorFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        unsetBackgroundColor(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateRecordStateFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateRecordState(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateRecordCallLogsFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateRecordCallLogs(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateRecordFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateRecord(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSearchFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        search(titleEditor, contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callMatchPreviousFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        matchPrevious(titleEditor, contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callMatchNextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        matchNext(titleEditor, contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callClearSearchResultFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        clearSearchResult(titleEditor, contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callDeleteNodeByAttachIdFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        deleteNodeByAttachId(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertPaintFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertPaint(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdatePaintFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updatePaint(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callDeletePaintFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        deletePaint(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateOverlayPaintHeightFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        addEmptyLinesForPaint(titleEditor, contentEditor, data as number)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertHintTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertHintText(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertPhoneHintTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertPhoneHintText(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callNotifyIdleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        ActiveObserver.instance.scrollIdle.value = data === '0';
        responseCallback("done");
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateContentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const info: ContentInfo = JSON.parse(data as string)
        console.log('callUpdateContentFromJava',info);
        let transContent =  Constants.DEFAULT_CONTENT_PLACEHOLDER
        ActiveObserver.instance.shouShowCard.value = !info.isDoingSummary
        const pageResults: PageResult[] = (info.pageResults !== "") ? JSON.parse(info.pageResults) : []
        const subAttachments: AttachmentAttr[] = (info.subAttachments !== "") ? JSON.parse(info.subAttachments) : []
        const pictureAttrs: PictureAttr[] = (info.pictureAttrs !== "") ? JSON.parse(info.pictureAttrs) : []
        var transTitle = Constants.DEFAULT_TITLE_PLACEHOLDER
        if (info.title !== "") {
            transTitle = HtmlHelper.convertTitle2Tiptap(info.title)
        }

        if (info.content !== "") {
            const mergedContent = info.content
            transContent = HtmlHelper.dataTransform(info.guid, mergedContent, subAttachments, pageResults, pictureAttrs)
        }
        titleEditor
            .chain()
            .clearContent()
            .setContent(transTitle, null, { preserveWhitespace: "full" })
            .command(({ commands }) => {
                commands.clearHistory()
                return true
            })
            .setMeta("addToHistory", false)
            .run()
        contentEditor
            .chain()
            .clearContent()
            .blur()
            .setContent(transContent, null, { preserveWhitespace: "full" })
            .command(({ commands }) => {
                commands.clearHistory()
                return true
            })
            .setMeta("addToHistory", false)
            .setMeta("autoLinkOnContentInitiation", true)
            .run()
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callDrawDoodleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        drawDoodle(contentEditor)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetAttachmentsFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callGetAttachmentsFromJava start`, Date.now)
        const attachment = getAllAttachments(contentEditor)
        responseCallback(attachment)
        console.log(`callGetAttachmentsFromJava end`, Date.now)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetSummaryEntityFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setSummaryEntity(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetEditorsHeightFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const titleEditorHeight = getEditorHeight(titleEditor, true)
        const contentEditorHeight = getEditorHeight(contentEditor, false)
        console.log(`callGetEditorsHeightFromJava:${titleEditorHeight}, ${contentEditorHeight}`)
        responseCallback(JSON.stringify([titleEditorHeight, contentEditorHeight]))
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertSummaryStreamTipFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        insertSummaryStreamTip(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateSummaryStreamTipFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        updateSummaryStreamTip(contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callDeleteSummaryStreamTipFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        deleteSummaryStreamTip(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSetSummaryContentWithAnimation', function (data: string, responseCallback: (arg0: string) => void) {
        insertSummaryContent(contentEditor,data)
        responseCallback("done")
    });
    // AI摘要是否完成（包含文字，附件卡片等） 逐渐转移到removeBackGround属性上
    (window as any).WebViewJavascriptBridge.registerHandler('callSetAiSummaryStartStop', function (data: any, responseCallback: (arg0: string) => void) {
        if (data === "false") {
            removeSummaryStreamTipBackgroundStyle()
            setBottomPlaceHolderHeight(true)
        }
        responseCallback("done");
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callEditorInterceptClickFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        interceptEditorClickEvent(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callRecordDisableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        disableRecord(contentEditor, data as string)
        responseCallback("done")
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callCaptureElementFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callCaptureElementFromJava, data:${data}`)
        captureElement(data, responseCallback)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callMoveAttachToSelectionFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callMoveAttachToSelectionFromJava, data:${data}`)
        const moveAttachInfo: MoveAttachInfo = JSON.parse(data)
        // if (titleEditor.view.hasFocus()) {
        //     //焦点/光标在标题栏上，则先将焦点切换到正文前面
        //     console.log('no focus on content editor')
        //     contentEditor.commands.focus('start')
        // } else if (!contentEditor.view.hasFocus()) {
        //     Log.i('TipTap', `callMoveAttachToSelectionFromJava, content has no focus!`)
        //     contentEditor.commands.focus('end')
        // }
        const result = contentEditor.chain().setSelectionAfterTable().moveAttachToSelection(moveAttachInfo.type, moveAttachInfo.attachId, moveAttachInfo.originPos).run()
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetSelectedTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callGetSelectedTextFromJava, data:${data}`)
        const result = getSelectedText(data as number, titleEditor, contentEditor)
        responseCallback(result)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSelectRangeTextAIGCFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callSelectRangeTextAIGCFromJava, data:${data}`)
        const range: EditorRang = JSON.parse(data)
        setAigcSelecitonColor()
        const result = selectRangeText(range.from, range.to, range.editor as number, titleEditor, contentEditor)
        responseCallback(result)
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callGetAllHTMLFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setTimeout(() => {
            let parseRes = ""
            const contentText = serializerContentToPlainText(contentEditor.view, contentEditor.state.doc.content)
            const titleText = serializerContentToPlainText(titleEditor.view, titleEditor.state.doc.content)
            parseRes = titleText + '\n' + contentText
            console.log(`callGetAllHTMLFromJava,titleText:${titleText}  contentText:${contentText}  parseRes:${parseRes}`)
            responseCallback(parseRes)
        })
    });
    (window as any).WebViewJavascriptBridge.registerHandler('callSelectAndGetAllTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callSelectAndGetAllTextFromJava, data:${data}`)
        // @ts-ignore
        let { text, attachMap } = selectAndGetAllText(data as number, titleEditor, contentEditor, false)
        setAigcSelecitonColor()
        ActiveObserver.instance.aigcAttachSelectionMap = attachMap
        ActiveObserver.instance.otherScenesTextColorHighlight.value = (ActiveObserver.instance.aigcTextUiModeApplyCss.value == 0) ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255,255,255,0.12)'
        ActiveObserver.instance.isUseAigcFunction.value = true
        setTimeout(() => {
            responseCallback(getSelectionHtml(contentEditor,false))
        })
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSelectAndGetForwardAllTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callSelectAndGetForwardAllTextFromJava, data:${data}`)
        setAigcSelecitonColor()
        let { text, attachMap } = selectAndGetForwardAllText(data as number, titleEditor, contentEditor, false)
        ActiveObserver.instance.otherScenesTextColorHighlight.value = (ActiveObserver.instance.aigcTextUiModeApplyCss.value == 0) ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255,255,255,0.12)'
        ActiveObserver.instance.isUseAigcFunction.value = true
        ActiveObserver.instance.aigcAttachSelectionMap = attachMap
        setTimeout(() => {
            responseCallback(getSelectionHtml(contentEditor,true))
        })
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callScrollToBottomFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callScrollToBottomFromJava, data:${data}`)
        const scrollToBottomInfo: ScrollToBottomInfo = JSON.parse(data)
        if (scrollToBottomInfo.scrollToBottom) {
            const { height } = useWindowSize()
            const marginBottom = scrollToBottomInfo.marginBottom + scrollToBottomInfo.marginExtra
            const bottomOffset = marginBottom - ActiveObserver.instance.bottomPlaceholderDefaultHeight.value
            if (bottomOffset > 0) {
                ActiveObserver.instance.bottomPlaceholderExtraHeight.value = bottomOffset
            }
            const stopWatch = watchImmediate(height, (newHeight, oldHeight) => {
                console.log(`callScrollToBottomFromJava, containerHeight changed! new:${newHeight}, old:${oldHeight}`)
                contentEditor.commands.scrollToBottomOffset(scrollToBottomInfo.marginBottom + scrollToBottomInfo.marginExtra)
                setTimeout(() => {
                    stopWatch()
                    console.log(`callScrollToBottomFromJava stopWatch`)
                }, 0);
            })
        } else {
            ActiveObserver.instance.bottomPlaceholderExtraHeight.value = 0
        }
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteStartFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteStartFromJava, data:${data}`)
        const aigcGenerateMargin: AIGCGenerateMargin = JSON.parse(data)
        ActiveObserver.instance.aigcTextRenderFinish.value = false
        console.log(`callOnAIGCRewriteStartFromJava, marigin:${aigcGenerateMargin.margin} ,aigcOption:${aigcGenerateMargin.aigcOption}, isExport: ${aigcGenerateMargin.isExport}`)
        // todo_aigc 开始生成，传入margin参数表示生成中SnackBar距离屏幕底部距离
        contentEditor.commands.aigcShowContent(aigcGenerateMargin.margin, aigcGenerateMargin.aigcOption)
        let result = true
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteFinishFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteFinishFromJava `)
        // todo_aigc 生成结束 无参数
        contentEditor.commands.aigcAddContentFinish(JSON.parse(data))
        setTimeout(() => {
            const aigcContainer = document.getElementById('aigc-text')
            // aigcContainer === null || 内容为空 就 返回false
            responseCallback(String((aigcContainer && aigcContainer.innerText !== '') || "false"))
        });
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteResultFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteResultFromJava, data:${data}`)
        const aigcResult: AIGCResult = JSON.parse(data)
        console.log(`callOnAIGCRewriteResultFromJava, result:${aigcResult.content} isFinish:${aigcResult.isFinish} `)
        /**
         * 传递过来的 & > < 符号可能是html代表的实体字符可能存在嵌套, 需要将其转义
         * 先转义 & 符号(可能存在 &amp;gt; 的情况)
        */
        aigcResult.content = replaceEscapeCharaterToEntity(aigcResult.content)
        // todo_aigc 接收生成结果，拼接content上屏，isFinish为true表示生成完
        contentEditor.commands.aigcAddContent(aigcResult)
        Log.d("TipTap", `callOnAIGCRewriteResultFromJava receive data-isFinish: ${aigcResult.isFinish}`, true);
        aigcResult.isFinish && invoke(async () => {
            try {
                Log.d("TipTap", `callOnAIGCRewriteResultFromJava rendering`, true);
                await until(ActiveObserver.instance.aigcTextRenderFinish).toBe(true)
                setTimeout(() => {
                    Log.d("TipTap", `callOnAIGCRewriteResultFromJava render finish call back`, true);
                    responseCallback("true")
                }, 150);
            } catch (error) {
                Log.d("TipTap", `callOnAIGCRewriteResultFromJava render fail`, true);
                responseCallback("false")
            }
        })

        // let result = true
        // responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteDeleteFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteDeleteFromJava, data:${data}`)
        // todo_aigc 生成结果菜单，点击了删除按钮，此时要移除生成结果区域
        contentEditor.commands.aigcDelete(true)
        ActiveObserver.instance.otherScenesTextColorHighlight.value = ''
        ActiveObserver.instance.isUseAigcFunction.value = false
        let result = true
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteInsertFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteInsertFromJava, data:${data}`)
        const beforeLength = contentEditor.storage.characterCount.characters()
        const para: InsertParas = JSON.parse(data)
        // todo_aigc 生成结果菜单，点击了插入当前位置，此时要将生成结果插入笔记生成结果的区域
        ActiveObserver.instance.aigcTextInsertCompleted.value = false
        contentEditor.commands.aigcInsertText(para.subStringLength, false)
        invoke(async () => {
            try {
                await until(ActiveObserver.instance.aigcTextInsertCompleted).toBe(true, { timeout: 2000, throwOnTimeout: true })
                const afterLength = contentEditor.storage.characterCount.characters()
                ActiveObserver.instance.otherScenesTextColorHighlight.value = ''
                responseCallback(String(afterLength - beforeLength))
            } catch (error) {
                const afterLength = contentEditor.storage.characterCount.characters()
                ActiveObserver.instance.otherScenesTextColorHighlight.value = ''
                responseCallback(String(afterLength - beforeLength))
            }
        })
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCRewriteReplaceFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCRewriteReplaceFromJava, data:${data}`)
        const beforeLength = contentEditor.storage.characterCount.characters()
        ActiveObserver.instance.aigcTextReplaceCompleted.value = false
        // todo_aigc 生成结果菜单，点击了插入当前位置，此时要将生成结果替换选中区域的文本
        contentEditor.commands.aigcInsertText(0, true)
        invoke(async () => {
            try {
                await until(ActiveObserver.instance.aigcTextReplaceCompleted).toBe(true, { timeout: 2000, throwOnTimeout: true })
                const afterLength = contentEditor.storage.characterCount.characters()
                responseCallback(String(afterLength - beforeLength))
            } catch (error) {
                const afterLength = contentEditor.storage.characterCount.characters()
                responseCallback(String(afterLength - beforeLength))
            }
        })
    });

    // 选中ai生成框内的所有内容
    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCSelectAllTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callOnAIGCSelectAllTextFromJava, data:${data}`)
        const aigcTextContent = document.getElementById('aigcTextContent')
        if(!aigcTextContent) return responseCallback("false")
        var range = document.createRange();
        range.selectNodeContents(aigcTextContent);
        var selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        responseCallback("true")
    });
        // 获取文档选中的html文本
    (window as any).WebViewJavascriptBridge.registerHandler('callOnAIGCGetSelectHtmlTextFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        // const result = getDocumentSelectionHtml()
        let result = tiptapHtml2NoteHtml('', getDocumentSelectionHtml())?.content || ''
        responseCallback(result)
    });

    // 获取aigc弹框内显示的内容(html格式)
    (window as any).WebViewJavascriptBridge.registerHandler('callOnGetAIGCHTMLContentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const aigcTextContent = document.getElementById('aigcTextContent')
        if(!aigcTextContent) return responseCallback("")
        let html = aigcTextContent.childElementCount === 1 ? aigcTextContent.firstElementChild.innerHTML : aigcTextContent.innerHTML
        let result = tiptapHtml2NoteHtml('', html)?.content || ''
        Log.d("TipTap", `callOnGetAIGCHTMLContentFromJava result: ${result}`, true);
        responseCallback(result)
    });


    (window as any).WebViewJavascriptBridge.registerHandler('callUpdateElementAttributesFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callUpdateElementAttributesFromJava, data:${data}`)
        const result = updateElementAttributes(data.toString())
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callDecreaseIndentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callDecreaseIndentFromJava: `)
        const  result = decreaseIndent(titleEditor, contentEditor)
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callIncreaseIndentFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callIncreaseIndentFromJava: `)
        const result = increaseIndent(titleEditor, contentEditor)
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetUiModeFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setUiMode(titleEditor, contentEditor, data as string)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callInsertTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        console.log(`callInsertTableFromJava: attachId=${data as string}`)
        const result = doInsertTable(contentEditor, data as string)
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callAddRowInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = contentEditor.chain().focus().addRowAfter().run()
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callAddColumnInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = contentEditor.chain().focus().addColumnAfter().run()
        removeActiveStyle()
        responseCallback(result ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callRemoveRowInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = contentEditor.chain().focus().deleteRow().run()
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callRemoveColumnInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const result = contentEditor.chain().focus().deleteColumn().run()
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callMoveRowInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const dir = Number(data)
        const result = contentEditor.chain().focus().moveTableRow(dir).run()
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callMoveColumnInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const dir = Number(data)
        const result = contentEditor.chain().focus().moveTableColumn(dir).run()
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callCutInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const event = new ClipboardEvent('cut', {
            bubbles: true,
            cancelable: true,
            clipboardData: new DataTransfer()
        })
        const result = contentEditor.view.dom.dispatchEvent(event)
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callCopyInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const event = new ClipboardEvent('copy', {
            bubbles: true,
            cancelable: true,
            clipboardData: new DataTransfer()
        })
        const result = contentEditor.view.dom.dispatchEvent(event)
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callPasteInTableFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const pasteResult: PasteResult = JSON.parse(data)
        let result = pasteResult.pasted
        if (!pasteResult.pasted) {
            if (pasteResult.clipboardData) {
                result = pasteClipboardData(contentEditor.view, false, pasteResult.clipboardData)
            }
        }
        responseCallback(result ? "true" : "false")
        removeActiveStyle()
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callShareTableToPictureFromJava', async function (data: any, responseCallback: (arg0: string) => void) {
        console.log('callShareTableToPictureFromJava')
        const result = await generateImageFromSelectedTable(contentEditor)
        console.log('generate-image-from-selected-table', result)
        //这里执行分享表格为图片的操作
        responseCallback(JSON.stringify(result))
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnActionItemClickedBeforeFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const actionId = data.toString()
        let currEditor: Editor
        if (contentEditor.view.hasFocus()) {
            currEditor = contentEditor
        } else {
            currEditor = titleEditor
        }
        currEditor.commands.actionItemClickedBefore(actionId)
        responseCallback('done')
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnActionItemClickedAfterFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const actionId = data.toString()
        let currEditor: Editor
        if (contentEditor.view.hasFocus()) {
            currEditor = contentEditor
        } else {
            currEditor = titleEditor
        }
        currEditor.commands.actionItemClickedAfter(actionId)
        responseCallback('done')
    });

    // 设置表格的背景色
    (window as any).WebViewJavascriptBridge.registerHandler('callSetTableColorFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        responseCallback(contentEditor.chain().setTableCellBackground(JSON.parse(data)).run() ? "true" : "false")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnTableMenuDismissFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        const rowDotElement = queryTableRowDotElement()
        if (rowDotElement) {
            rowDotElement.classList.remove(NAME_SHOW_ROW_DOT_MENU_SELECTED)
        }

        const columnDotElement = queryTableColumnDotElement()
        if (columnDotElement) {
            columnDotElement.classList.remove(NAME_SHOW_COLUMN_DOT_MENU_SELECTED)
        }
        responseCallback('done')
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callOnTableSelectMenuDismissFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        removeSelectionCellBorderWidget()
        responseCallback('done')
    });
}

export function useRegisterCssAttr() {
    (window as any).WebViewJavascriptBridge.registerHandler('callSetDensityScaleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        ActiveObserver.instance.densityScale.value = data as number
        setRootFontSize(ActiveObserver.instance.editorScale.value, ActiveObserver.instance.densityScale.value)
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler('callSetEditorScaleFromJava', function (data: any, responseCallback: (arg0: string) => void) {
        setEditorScale(data as number)
        setRootFontSize(data as number);
        responseCallback("done")
    });

    (window as any).WebViewJavascriptBridge.registerHandler("callSetBasicCssParamsFromJava", function (data: any, responseCallback: (arg0: string) => void) {
        setBasicCssParams(data as string)
        responseCallback("done");
    });

    (window as any).WebViewJavascriptBridge.registerHandler("callSetSkinCssParamsFromJava", function (data: any, responseCallback: (arg0: string) => void) {
        setSkinCssParams(data as string)
        responseCallback("done");
    });

    (window as any).WebViewJavascriptBridge.registerHandler("callEnableImageAnimationFromJava", function (data: any, responseCallback: (arg0: string) => void) {
        const enable: boolean = JSON.parse(data)
        ActiveObserver.instance.showImgWithAnimation.value = enable
        responseCallback("done");
    });

    (window as any).WebViewJavascriptBridge.registerHandler("callSetUserScrollFromJava", function (data: any, responseCallback: (arg0: string) => void) {
        const isUserScroll = (data as string) == 'true'
        ActiveObserver.instance.isUserScrolling.value = isUserScroll
        responseCallback("done");
    });
}

// 皮肤字体缓存，避免创建重复字体样式
const SKIN_FONT_CACHE = []

function setTitleFont(font: string, isSwitchToSystemFontFromThird: boolean) {
    console.log(`setTitleFont: ${font}, isSwitchToSystemFontFromThird=${isSwitchToSystemFontFromThird}`)
    ActiveObserver.instance.boldFontWeight.value = ActiveObserver.instance.defaultBoldFontWeight
    ActiveObserver.instance.normalFontWeight.value = ActiveObserver.instance.defaultNormalFontWeight
    switch (font) {
        // roboto字体，由于系统字体默认op sans和roboto西文字体，采用组合字体
        case Constants.FONT_ROBOTO:
            // 只有在三方字体切roboto字体，为了正常显示中文，才需要加载SysSans-Hans-Regular.ttf字体。
            // 考虑到此时分步加载，中文粗体会从三方粗体->系统非粗体->系统粗体，视觉体验反而不好，所以牺牲点加载速度，一步到位加载SysSans-Hans-Regular.ttf字体
            ActiveObserver.instance.titleFont.value = isSwitchToSystemFontFromThird ? Constants.Sys_Regular_Font + ", " + Constants.Sys_Sans_Hans_Font : Constants.Sys_Regular_Font
            break;
        // 第三方字体
        case Constants.FONT_THIRD:
            ActiveObserver.instance.titleFont.value = Constants.Sys_Regular_Font
            break;
        // sys sans
        case Constants.FONT_SYS_SANS:
        case Constants.FONT_SYS_SANS_AUTO:
            // Step1: 先加载轻量的SysSans-En-Regular.ttf
            ActiveObserver.instance.titleFont.value = Constants.Sys_Sans_Font
            // Step2: 再加载最终的重量级的SysSans-Hans-Regular.ttf
            // 因为只有SysSans-Hans-Regular.ttf加载完成后，才能响应无极粗细变化，所以这中间会出现中文字体粗细的变化，暂无法避免。
            asyncLoadCustomFont(Constants.Sys_Sans_Hans_Font, (loadedFont) => {
                ActiveObserver.instance.titleFont.value = loadedFont.family
            }, null)
            break;
        // 由于系统字体默认op sans和one sans西文字体，采用组合字体
        case Constants.FONT_ONE_SANS:
            // 只有在三方字体切One Sans字体，为了正常显示中文，才需要加载SysSans-Hans-Regular.ttf字体。
            // 考虑到此时分步加载，中文粗体会从三方粗体->系统非粗体->系统粗体，视觉体验反而不好，所以牺牲点加载速度，一步到位加载SysSans-Hans-Regular.ttf字体
            ActiveObserver.instance.titleFont.value = isSwitchToSystemFontFromThird ? Constants.One_Sans_Font + ", " + Constants.Sys_Sans_Hans_Font : Constants.One_Sans_Font
            break;
        // 皮肤所带字体
        default:
            getFontNameForSkinFont(font, fontName => {
                // 若字体已经定义过，则直接引用，不再生成@font-face
                if (SKIN_FONT_CACHE.find(name => name == fontName)) {
                    ActiveObserver.instance.titleFont.value = fontName;
                    return
                }
                SKIN_FONT_CACHE.push(fontName)
                const style = createFontStyle(fontName, font);
                document.head.appendChild(style);
                ActiveObserver.instance.titleFont.value = fontName;
            })
            break;
    }
}

function setContentFont(font: string, isSwitchToSystemFontFromThird: boolean) {
    console.log(`setContentFont: ${font}, isSwitchToSystemFontFromThird=${isSwitchToSystemFontFromThird}`)
    ActiveObserver.instance.boldFontWeight.value = ActiveObserver.instance.defaultBoldFontWeight
    ActiveObserver.instance.normalFontWeight.value = ActiveObserver.instance.defaultNormalFontWeight
    switch (font) {
        // roboto字体，由于系统字体默认op sans和roboto西文字体，采用组合字体
        case Constants.FONT_ROBOTO:
            // 只有在三方字体切roboto字体，为了正常显示中文，才需要加载SysSans-Hans-Regular.ttf字体。
            // 考虑到此时分步加载，中文粗体会从三方粗体->系统非粗体->系统粗体，视觉体验反而不好，所以牺牲点加载速度，一步到位加载SysSans-Hans-Regular.ttf字体
            ActiveObserver.instance.contentFont.value = isSwitchToSystemFontFromThird ? Constants.Sys_Regular_Font + ", " + Constants.Sys_Sans_Hans_Font : Constants.Sys_Regular_Font
            ActiveObserver.instance.fontLoaded.value = true
            break;
        // 第三方字体
        case Constants.FONT_THIRD:
            ActiveObserver.instance.contentFont.value = Constants.Sys_Regular_Font
            ActiveObserver.instance.fontLoaded.value = true
            break;
        // sys sans
        case Constants.FONT_SYS_SANS:
        case Constants.FONT_SYS_SANS_AUTO:
            // Step1: 先加载轻量的SysSans-En-Regular.ttf
            ActiveObserver.instance.contentFont.value = Constants.Sys_Sans_Font
            // Step2: 再加载最终的重量级的SysSans-Hans-Regular.ttf
            // 因为只有SysSans-Hans-Regular.ttf加载完成后，才能响应无极粗细变化，所以这中间会出现中文字体粗细的变化，暂无法避免。
            asyncLoadCustomFont(Constants.Sys_Sans_Hans_Font, (loadedFont) => {
                ActiveObserver.instance.contentFont.value = loadedFont.family
                ActiveObserver.instance.fontLoaded.value = true
            }, (error) => {
                console.log(`asyncLoadCustomFont error:${error}`)
                ActiveObserver.instance.fontLoaded.value = true
            })
            break;
        // 由于系统字体默认op sans和one sans西文字体，采用组合字体
        case Constants.FONT_ONE_SANS:
            // 只有在三方字体切One Sans字体，为了正常显示中文，才需要加载SysSans-Hans-Regular.ttf字体。
            // 考虑到此时分步加载，中文粗体会从三方粗体->系统非粗体->系统粗体，视觉体验反而不好，所以牺牲点加载速度，一步到位加载SysSans-Hans-Regular.ttf字体
            ActiveObserver.instance.contentFont.value = isSwitchToSystemFontFromThird ? Constants.One_Sans_Font + ", " + Constants.Sys_Sans_Hans_Font : Constants.One_Sans_Font
            ActiveObserver.instance.fontLoaded.value = true
            break;
        // 皮肤所带字体
        default:
            getFontNameForSkinFont(font, fontName => {
                // 若字体已经定义过，则直接引用，不再生成@font-face
                if (SKIN_FONT_CACHE.find(name => name == fontName)) {
                    ActiveObserver.instance.contentFont.value = fontName;
                    ActiveObserver.instance.fontLoaded.value = true
                    return
                }
                SKIN_FONT_CACHE.push(fontName)
                const style = createFontStyle(fontName, font);
                document.head.appendChild(style);
                ActiveObserver.instance.contentFont.value = fontName;
                asyncLoadCustomFont(fontName, (loadedFont) => {
                    console.log(`skinFont:${fontName} load finish`)
                    ActiveObserver.instance.fontLoaded.value = true
                }, () => {
                    ActiveObserver.instance.fontLoaded.value = true
                })
            })
            break;
    }
}


function getFontNameForSkinFont(path: string, handle: (args: string) => void) {
    // 获取皮肤字体文件名称
    const name =
        path.split("/").pop()?.replace(RegExp("(.ttf)|(.woff)|(.woff2)|(.eot)|(.otf)|(.svg)", "ig"), "") || "";
    console.log(`skin font name: ${name}`)
    if (name) {
        // 字体文件名称不能数字或者标点符号开头，使用拼接的方式
        handle(`custom-font-${name}`);
    }
}

export function asyncLoadCustomFont(fontFaimly: string, callbackfn: (loadedFont: FontFace) => void, loadFail: (reason: string) => void | null) {
    const now = Date.now()
    let found = false
    document.fonts.forEach((font) => {
        if (font.family == fontFaimly) {
            found = true
            console.log(`asyncLoadCustomFont: font.family=${font.family}, status=${font.status}`)
            if ((font.status == "unloaded" || font.status == "error" || font.status == "loading")) {
                font.load().then((loadedFont) => {
                    console.log(`asyncLoadCustomFont: load ${loadedFont.family} completed. cost=${Date.now() - now}ms`)
                    callbackfn(loadedFont)
                }).catch((reason) => {
                    Log.e('Tiptap', `Failed to load font: ${fontFaimly}, reason:${reason}`, true)
                    if (loadFail) {
                        loadFail(reason)
                    }
                })
            } else if (font.status == 'loaded') {
                console.log(`asyncLoadCustomFont: ${font.family} already loaded`)
                callbackfn(font)
            }
        }
    })

    if (!found) {
        if (loadFail) {
            loadFail(`${fontFaimly} not found in document`)
        }
    }
}


/**
 * 向标题编辑器和内容编辑器插入内容
 * @param {Editor} titleEditor 标题编辑器
 * @param {Editor} contentEditor 内容编辑器
 * @param {String} textJson 文本信息
 * @returns
 */
function setText(titleEditor: Editor, contentEditor: Editor, textJson: string): boolean {
    const content: InputContent = JSON.parse(textJson)
    var editor: Editor | undefined
    // 兜底使用contentEditor 否则中转站拖入多个可能会导致editor丢失
    editor = getFocusedEditor(titleEditor, contentEditor).editor || contentEditor
    if (editor == undefined) {
        Log.e('TipTap', `setText fail! editor:${content.focusedEditor}, isFinal:${content.isFinal}, insertToEnd:${content.insertToEnd}, keepSelection:${content.keepSelection}`)
        return false
    }
    if (content.insertToEnd == true) {
        editor.commands.focus('end')
    }
    const { from, to, $to, empty } = editor.state.selection
    const { selectionFrom } = ActiveObserver.instance
    // 判断如果是在空行，再语音输入一个换行，则ignore。将原来Native的逻辑移植到JS
    const ignore = (content.isFinal == true && content.text === '\n' && $to.parent.textContent === '') ? true : false
    console.log(`setText: from=${from}, to=${to}, empty=${empty}, selectionFrom=${selectionFrom.value}, ignore=${ignore}, content=${JSON.stringify(content)}`, editor)
    // 在附件前后的时候, from值会存在问题
    if (selectionFrom.value == undefined) {
        // Boolean(editor.state.doc.resolve(from).node(1)) === false 当前选中的是一个附件, 先在to的位置上插入一个空行,然后再将焦点聚焦在这个空行内
        if (editor.state.doc.resolve(from).node(1)) {
            selectionFrom.value = from
        } else {
            editor.chain().insertContentAt(to, '<p></p>').focus(to + 1).run()
            selectionFrom.value = to + 1
        }
    }
    if (!ignore) {
        let insertText = content.text
        if (content.isDragDrop && isTitleEditorView(editor.view)) {
            //拖拽文字内容到标题区域，需要将内容中的换行转为空格，避免在标题栏区域插入很多空行
            insertText = content.text.replace(/\n/g, ' ');
        }
        const dragTextInfo = ActiveObserver.instance.dragTextInfo
        if (isAttachElementFocus(document.activeElement)) {
            if (dragTextInfo && dragTextInfo.selection.from > from) {
                dragTextInfo.dragEditor.commands.deleteRange({ from: dragTextInfo.selection.from, to: dragTextInfo.selection.to })
                attachKeyMapEvent.insertText({ data: insertText }, editor)
            } else {
                attachKeyMapEvent.insertText({ data: insertText }, editor)
                dragTextInfo && dragTextInfo.dragEditor.commands.deleteRange({ from: dragTextInfo.selection.from, to: dragTextInfo.selection.to })
            }
            // 千万不要把这里的editor.state.selection.to 改成 to 否则插入文字位置会有问题
            ActiveObserver.instance.selectionFrom.value = editor.state.selection.to - insertText.length
        } else {
            if (dragTextInfo && dragTextInfo.selection.from > from) {
                dragTextInfo.dragEditor.commands.deleteRange({ from: dragTextInfo.selection.from, to: dragTextInfo.selection.to })
                editor.chain().insertPlainText(insertText, ActiveObserver.instance.selectionFrom.value, to, content.keepSelection).run()
            } else {
                editor.chain().insertPlainText(insertText, ActiveObserver.instance.selectionFrom.value, to, content.keepSelection).run()
                dragTextInfo && dragTextInfo.dragEditor.commands.deleteRange({ from: dragTextInfo.selection.from, to: dragTextInfo.selection.to })
            }
        }
    }
    if (content.isFinal) {
        selectionFrom.value = undefined
    }
    return true
}

function clearContent(editor: Editor) {
    console.log(`clearContent:`)
    editor.commands.clearContent()
}

export function insertImage(editor: Editor, imageJson: string) {
    setTimeout(() => {
        console.log(`insertImage: ${imageJson}`)
        const attachData: AttachData = JSON.parse(imageJson)
        const imageInfo: ImageInfo = attachData.image
        const imageOption = {
            src: imageInfo.src,
            attachid: imageInfo.attachId,
            width: imageInfo.width,
            height: imageInfo.height,
            srcExist: imageInfo.srcExist,
            placeholderRelativePath: imageInfo.placeholderRelativePath,
            useIntersectionObserver: false
        }
        editor.chain().command(({commands}) => {
            if (!isEditMode() && attachData.insertToEndInNonEditMode) {
                commands.focus('end')
            }
            return true
        }).insertImage(imageOption).run()
    },25)
}

export function updateImage(editor: Editor, imageJson: string) {
    console.log(`replaceImage: in ${imageJson}`)
    const attr: AttrMenuData = JSON.parse(imageJson)
    const imageInfo = attr.image
    editor.commands.replaceImage({ src: attr.image.src, attachid: attr.image.attachId, oldAttachId: attr.clickAttachId, width: imageInfo.width, height: imageInfo.height })
}

export function insertVideoCard(editor: Editor, jsonStr: string) {
    console.log('insertVideoCard',jsonStr)
    const attachData: AttachData = JSON.parse(jsonStr)
    const videoInfo: VideoCardAttr = attachData.videoCard
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertVideoCard(videoInfo).run()
}


export function updateVideoCard(editor: Editor, jsonStr: string) {
    console.log('updateVideoCard',jsonStr)
    const attachData: AttachData = JSON.parse(jsonStr)
    const videoInfo: VideoCardAttr = attachData.videoCard
    editor.chain().updateVideoCard(videoInfo).setMeta("addToHistory", false).run()
}


export function updateCard(editor: Editor, cardJson: string) {
    console.log(`updateCard: in ${cardJson}`)
    const attr: AttrMenuData = JSON.parse(cardJson)
    editor.commands.updateCard(attr.clickAttachId, attr.card)
}


/**
 * 图片替换卡片
 * @param editor
 * @param attrJson
 */
export function replaceNodeByImage(editor: Editor, attrJson: string) {
    console.log(`replaceNodeByImage: attr=${attrJson}`)
    const menuData: AttrMenuData = JSON.parse(attrJson)

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === menuData.clickAttachId) {
            console.log(`replaceNodeByImage: find target`)
            const range = { from: pos, to: pos + node.nodeSize }
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertImage({ src: menuData.image.src, attachid: menuData.image.attachId, width: menuData.image.width, height: menuData.image.height, useIntersectionObserver: false }).run()
        }
    })
}

export function replaceNodeByCard(editor: Editor, attrJson: string) {
    console.log(`replaceNodeByCard: attr=${attrJson}`)
    const menuData: AttrMenuData = JSON.parse(attrJson)

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === menuData.clickAttachId) {
            console.log(`replaceNodeByCard: find target`)
            const range = { from: pos, to: pos + node.nodeSize }
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertCard(menuData.card).run()
        }
    })
}

export function replaceAttachmentByText(editor: Editor, imageJson: string) {
    const attr: AttrMenuData = JSON.parse(imageJson)
    console.log(`replaceAttachmentByText: in attachId=${attr.clickAttachId},text=${attr.text}`)

    editor.state.doc.descendants((node, pos) => {
        if ( node.attrs.attachid === attr.clickAttachId) {
            console.log(`replaceAttachmentByText: find target`)
            const range = { from: pos, to: pos + node.nodeSize }
            editor.commands.deleteRange(range)
            editor.commands.insertContentAt(pos, attr.text)
            editor.commands.focus(pos)
        }
    })
}

export function replaceNodeByPaint(editor: Editor, attrJson: string) { // 将卡片、图片替换为涂鸦
    console.log(`replaceNodeByPaint: attr=${attrJson}`)
    const attr: UpdatePaintAttr = JSON.parse(attrJson)
    const oldAttachId = attr.oldPaintId
    const paintAttr = attr.newAttr

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === oldAttachId) {
            console.log(`replaceNodeByPaint: find target`)
            const range = { from: pos, to: pos + node.nodeSize }
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertPaint(paintAttr).run()
        }
    })
}

export function replaceNodeByDocument(editor: Editor, attrJson: string) { // 将卡片、图片替换为文档文件
    console.log(`replaceNodeByDocument: attr=${attrJson}`)
    const attr: UpdateAttachmentAttr = JSON.parse(attrJson)
    const oldAttachId = attr.oldAttachId
    const attachData = attr.attachData

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === oldAttachId) {
            console.log(`replaceNodeByDocument: find target`)
            const range = {from: pos, to: pos + node.nodeSize}
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertFileCard(attachData.fileCard).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
        }
    })
}

export function replaceNodeByVideo(editor: Editor, attrJson: string) { // 将卡片、图片替换为视频
    console.log(`replaceNodeByVideo: attr=${attrJson}`)
    const attr: UpdateAttachmentAttr = JSON.parse(attrJson)
    const oldAttachId = attr.oldAttachId
    const attachData = attr.attachData

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === oldAttachId) {
            console.log(`replaceNodeByVideo: find target`)
            const range = {from: pos, to: pos + node.nodeSize}
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertVideoCard(attachData.videoCard).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
            editor.chain().focus(pos).updateVideoCard(attachData.videoCard).setMeta("addToHistory", false).run()
        }
    })
}

export function replaceNodeByAudio(editor: Editor, attrJson: string) { // 将卡片、图片替换为音频
    console.log(`replaceNodeByAudio: attr=${attrJson}`)
    const attr: UpdateAttachmentAttr = JSON.parse(attrJson)
    const oldAttachId = attr.oldAttachId
    const attachData = attr.attachData

    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === oldAttachId) {
            console.log(`replaceNodeByAudio: find target`)
            const range = {from: pos, to: pos + node.nodeSize}
            editor.commands.deleteRange(range)
            editor.chain().focus(pos).insertRecord(attachData.record).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
        }
    })
}

function getHTML(editor: Editor): string {
    const html = editor.getHTML()
    console.log(`getHTML:${html}`)
    return html
}

function hasLinkCard(editor: Editor): boolean {
    let result = false
    editor.state.doc.descendants((node) => {
        if (isLinksCardNode(node)) {
            result = true
            return false
        }
        return true
    });
    return result
}

function getSlectedHtml(editor: Editor): string {
    const fragment = editor.view.state.selection.content()
    const result = JSON.stringify(fragment.toJSON())
    console.log(`getSlectedJson:${result}`)
    console.log(`getSlectedHtml:${fragment.toString()}`)
    return result
}

export function getText(editor: Editor): string {
    const doc = editor.state.doc
    const from = 0;
    const text = new StringBuilder();
    doc.content.forEach(itemNode => {
        if (isTableNode(itemNode)) {
            const tableText = getTextFromTable(itemNode)
            text.append(tableText + '\n');
        } else {
            const itemText = itemNode.textBetween(from, itemNode.content.size, '\n')
            text.append(itemText + '\n');
        }
    });
    const textResult = text.toString().trim();
    console.log(`getText:${textResult}`)
    return textResult
}

function getTextFromTable(tableNode: Node): String {
    const text = new StringBuilder();
    // 遍历表格的每一行
    tableNode.forEach(rowNode => {
        // 遍历每一行的每一个单元格
        const rowText = new StringBuilder();
        rowNode.forEach(cellNode => {
            // 获取单元格中的文本内容
            const cellText = getPureTextFromNode(cellNode);
            if (cellText !== null && cellText !== undefined && cellText !== '') {
                rowText.append(cellText + ' '); // 使用空格分隔单元格内容
                console.log(`getPureTextFromNode: ${text}`)
            }
        });
        text.append(rowText.toString().trim() + '\n'); // 每一行添加换行
    });

    return text.toString().trim();
}

const getPureTextFromNode = (node: any): string => {
    const text = new StringBuilder();

    node.content.forEach((childNode: any) => {
        if (isTableNode(childNode)) {
            text.append(getTextFromTable(childNode) + '\n');
        } else if (isParagrahNode(childNode) || isHeadingNodeInHelper(childNode) || isBlockquoteNode(childNode)) {
            text.append(childNode.textContent + '\n');
        } else if (isListNode(childNode)) {
            text.append(getPureTextFromList(childNode) + '\n');
        } else if (isCodeBlockNode(childNode)) {
            text.append(childNode.textContent + '\n');
        } else if (isHardBreakNode(childNode)) {
            text.append('\n');
        } else if (childNode.isText) {
            text.append(childNode.text + '\n');
        }
    });

    return text.toString().trim();
};

const getPureTextFromList = (listNode: any): string => {
    const text = new StringBuilder();
    listNode.content.forEach((itemNode: any) => {
        text.append(getPureTextFromNode(itemNode) + '\n');
    });
    return text.toString().trim();
};

export function getSelectText(titleEditor:Editor, contentEditor: Editor): string {
    const editor = getFocusedEditor(titleEditor, contentEditor).editor
    if (editor) {
        const { from, to } = editor.view.state.selection
        const text = editor.state.doc.textBetween(from, to, '\n')
        console.log(`getSelectText:${text}`)
        return text
    } else {
        return ""
    }
}
// 获取文档内选中的文本(包含tiptapComtent外被选中的内容)
function getDocumentSelectionHtml(): string{
    let selection = getSelection();
    // @ts-ignore
    if (!selection.baseNode) {
        console.log("没有选中任何文本")
        return ''
    }
    let range = null;

    if (selection.type === "Caret") {
      range = new Range();
      range.setStart(document.body.firstChild, 0);
      range.setEnd(selection.focusNode, selection.focusOffset);
    } else {
      range = selection.getRangeAt(0);
    }

    let fragment = range.cloneContents();
    let testDiv = document.createElement(selection.anchorNode.parentElement.nodeName);
    testDiv.appendChild(fragment);

    const copyNode = testDiv.cloneNode(true) as HTMLDivElement
    copyNode.innerHTML = copyNode.innerHTML.trim()

    let selectHtml = testDiv.innerHTML;
    if (copyNode.firstChild.nodeName === '#text') selectHtml = testDiv.outerHTML;
    console.log('get-document-select-html', selectHtml)
    return selectHtml;
}

export function getRecordDetailRect(editor: Editor, attachId: string): string {
    const detailButtons = editor.view.dom.querySelectorAll('.record-call-content')
    let rectString = ""

    if (detailButtons && detailButtons.length > 0) {
        detailButtons.forEach((button) => {
            const attrAttachId = button.getAttribute('attachid')
            if (attrAttachId == attachId) {
                const rect = button.getClientRects()[0]
                const viewportHeight = window.innerHeight || document.documentElement.clientHeight
                let isVisible = true
                if (rect.top < 0 || rect.bottom > viewportHeight) { //顶端滑出屏幕或底部滑出屏幕
                    isVisible = false
                }

                const detailRect = {
                    top: rect.top,
                    right: rect.right,
                    bottom: rect.bottom,
                    left: rect.left,
                    isVisible: isVisible
                }
                rectString = JSON.stringify(detailRect)
            }
        })
    }

    return rectString
}

function getRecordCardRect(editor: Editor, attachId: string): string {
    const recordCards = editor.view.dom.querySelectorAll('.record')
    let rectString = ""

    if (recordCards && recordCards.length > 0) {
        recordCards.forEach((card) => {
            const attrAttachId = card.getAttribute('attachid')
            if (attrAttachId == attachId) {
                const rect = card.getClientRects()[0]
                const viewportHeight = window.innerHeight || document.documentElement.clientHeight
                let isVisible = true
                if (rect.bottom <= 0 || rect.top >= viewportHeight) { //在屏幕完全不可见
                    isVisible = false
                }

                const recordRect = {
                    top: rect.top,
                    right: rect.right,
                    bottom: rect.bottom,
                    left: rect.left,
                    isVisible: isVisible
                }
                rectString = JSON.stringify(recordRect)
            }
        })
    }

    return rectString
}

export function clearFocus(titleEditor: Editor, contentEditor: Editor) {
    console.log('clearFocus');
    titleEditor.commands.blur()
    contentEditor.commands.blur()
    /**
     * 在点击附件右侧或者通过下一行行首删除操作 将光标移动到附件右侧
     * tiptapContent已经触发了onblur事件
     * 通过删除操作选中附件, 后 进行其他需要clearFocus操作(保存, 分享...)的时候, 不会走tiptap.onblur事件
     * 需要对附件重新取消选中
    */
    !ActiveObserver.instance.longTap && contentEditor.commands.removeAllSelectedBlock()
}

export function focus(titleEditor: Editor, contentEditor: Editor, focusInfoStr: string) {
    if(isAttachElementFocus(document.activeElement)) return

    const focusInfo: FocusInfo = JSON.parse(focusInfoStr)
    const { position, focusedEditor } = focusInfo
    console.log(`focus: ${JSON.stringify(focusInfo)}`);
    var editor: Editor | undefined
    switch (focusedEditor) {
        case Constants.UNKNOWN_FOCUSED:
            const focused = getFocusedEditor(titleEditor, contentEditor).editor
            editor = (focused == undefined) ? contentEditor : focused
            console.log(`focus editor:${editor}`)
            break;
        case Constants.TITLE_FOCUSED:
            editor = titleEditor
            break;
        case Constants.CONTENT_FOCUSED:
            editor = contentEditor
            break;
    }
    if (editor) {
        if (position === 'start') {
            editor.commands.focus('start')
        } else if (position === 'end') {
            // 如果文档的最后是一个附件  就将焦点聚焦在最后一个附件的右侧的元素(显示右侧光标的元素)
            if(isAttachNode(editor.state.doc.lastChild)) {
                const lastChild = queryTiptapContentElement().lastElementChild
                const after = lastChild.getElementsByClassName(afterClass)[0] as HTMLElement | null
                if(after) {
                    after.contentEditable = 'true'
                    after.focus()
                }
            }
            editor.commands.focus('end')
        } else if (position === '') {
            console.log(`focus undefined position:${editor.isFocused}`)
            if (!editor.isFocused) {
                //当前有focus时，不需要再focus，避免无效的滚动
                editor.commands.focus()
            }
        } else {
            const num = + position
            editor.commands.focus(num)
        }
    }
}

export function setTitleStyle(contentEditor: Editor, data: string) {
    console.log(`setTitleStyle: ${data}`);
    const titleInfo: TitleInfo = JSON.parse(data)
    const type = titleInfo.type
    const activate = titleInfo.titleStyle
    //contentSize只有fontsize,不需要额外判断
    let multiHeadingLevel = '1'
    switch (type) {
        case RichTextToolsConstant.TEXT_STYLE_TITLE:
            multiHeadingLevel = '1'
            break
        case RichTextToolsConstant.TEXT_STYLE_SUBTITLE:
            multiHeadingLevel = '2'
            break
        case RichTextToolsConstant.TEXT_STYLE_SMALL_TITLE:
            multiHeadingLevel = '3'
            break
        default:
            multiHeadingLevel = '1'
            break
    }
    if (activate) {
        contentEditor.chain().focus().unsetOnlyBlockquote().setMultiHeading({ multiHeading: multiHeadingLevel }).run()
    } else {
        contentEditor.chain().focus().unsetHeading().unsetOnlyBlockquote().run()
    }
    contentEditor.commands.updateNativeMarksStatus()
}

export function setBlockQuote(contentEditor: Editor, setValue: boolean) {
    if (setValue == true) {
        contentEditor.chain().focus().unsetHeading().setBlockquote().run()
    } else {
        contentEditor.chain().focus().unsetHeading().unsetBlockquote().run()
    }
    contentEditor.commands.updateNativeMarksStatus()
}

export function setContentStyle(contentEditor: Editor, data: string) {
    console.log(`setTitleStyle: ${data}`);
    contentEditor.chain().focus().unsetHeading().unsetOnlyBlockquote().run()
    contentEditor.commands.updateNativeMarksStatus()
}

export function setTextColor(editor: Editor, data: string) {
    console.log(`setTextColor:${data}`);
    if (data == 'default') {
        editor.chain().focus().unsetColor().run()
    } else {
        editor.chain().focus().setColor(`var(${data})`).run()
    }
}

export function getCursorPosition(editor: Editor): number[] {
    const {state, view} = editor;
    // 获取选区
    const {selection} = state;
    const {from} = selection;
    // 获取光标位置的 DOM 元素
    const coords = view.coordsAtPos(from);
    console.log(`bxx Cursor Position: X=${coords.left}, Y=${coords.top}`);
    return [coords.left, coords.top]
}
function setBold(editor: Editor) {
    console.log(`setBold:`);
    editor.chain().focus().setBold().run()
}

function unsetBold(editor: Editor) {
    console.log(`unsetBold:`);
    editor.chain().focus().unsetBold().run()
}

function setItalic(editor: Editor) {
    console.log(`setItalic:`);
    editor.chain().focus().setItalic().run()
}

function unsetItalic(editor: Editor) {
    console.log(`unsetItalic:`);
    editor.chain().focus().unsetItalic().run()
}

function setUnderline(editor: Editor) {
    console.log(`setUnderline:`);
    editor.chain().focus().setUnderline().run()
}

function unsetUnderline(editor: Editor) {
    console.log(`unsetUnderline:`);
    editor.chain().focus().unsetUnderline().run()
}


function setStrikethrough(editor: Editor) {
    console.log(`setStrikethrough:`);
    editor.chain().focus().setStrike().run()
}

function unsetStrikethrough(editor: Editor) {
    console.log(`unsetStrikethrough:`);
    editor.chain().focus().unsetStrike().run()
}
function setBackgroundColor(editor: Editor, color: string) {
    let setColor = color
    if (color.startsWith('#')) {
        setColor = hexToRgba(color)
    }
    console.log(`setBackgroundColor:${color}, target color: ${setColor}`);
    editor.chain().focus().setBackgroundColor(setColor).run()
}

function unsetBackgroundColor(editor: Editor) {
    console.log(`unsetBackgroundColor:`);
    editor.chain().focus().unsetBackgroundColor().run()
}

export function setFontSize(editor: Editor, fontSizeString: string) {
    const fontSize = parseFloat(fontSizeString)
    editor.chain().focus().setFontSize(fontSize).run()
}

export function setRootFontSize(editorScale: number, densityScale: number = ActiveObserver.instance.densityScale.value) {
    const fontSize = 16 * densityScale * editorScale + "px"
    document.documentElement.style.fontSize = fontSize
    console.log(`setRootFontSize: fontSize: ${fontSize}, densityScale: ${densityScale}, editorScale: ${editorScale}`)
}

export function toggleBulletList(editor: Editor) {
    Log.d("TipTap", `toggleBulletList: hasFocus:${editor.view.hasFocus()}, selection:${editor.state.selection.from}-${editor.state.selection.to}`);
    editor.chain().focus().toggleBulletList().run()
}

export function toggleBulletListHX(editor: Editor) {
    console.log(`toggleBulletListHX:`);
    Log.d("TipTap", `toggleBulletListHX: hasFocus:${editor.view.hasFocus()}, selection:${editor.state.selection.from}-${editor.state.selection.to}`);
    editor.chain().focus().toggleBulletListHX().run()
}

export function toggleOrderedList(editor: Editor) {
    Log.d("TipTap", `toggleOrderedList: hasFocus:${editor.view.hasFocus()}, selection:${editor.state.selection.from}-${editor.state.selection.to}`);
    editor.chain().focus().toggleOrderedList().run()
}

export function toggleTaskList(editor: Editor) {
    Log.d("TipTap", `toggleTaskList: hasFocus:${editor.view.hasFocus()}, selection:${editor.state.selection.from}-${editor.state.selection.to}`);
    const inTable = isInTable(editor.state)
    if(inTable){
        editor.chain().insertContentAt(editor.state.doc.content.size,'<p></p>').run()
        setTimeout(() => {
            editor.chain().focus('end').toggleTaskList().run()
        })
    }else{
        editor.chain().focus().toggleTaskList().run()
    }
}

export function test(editor: Editor) {
    const selectionInfo = JSON.parse(getSelectionInfo(editor))
    editor.chain().aigcReplaceRangeWithoutAttachment(selectionInfo.from, selectionInfo.to, "123").run()
}

export function setTextAlign(editor: Editor | null, alignment: string) {
    const alignOption: AlignOption = JSON.parse(alignment)
    let marks
    if (editor) {
        const { selection, storedMarks } = editor.state
        marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())
    }
    editor
        ?.chain().focusNoScroll().command(({ tr, commands }) => {
            commands.setTextAlign(alignOption.align)
            console.log(`setTextAlign: history=${alignOption.history}`)
            if (alignOption.history == false) {
                tr.setMeta("addToHistory", false)
            }
            return true
        }).command((state) => {
            if (marks) {
                state.tr.setStoredMarks(marks)
            }
            return true
        }).run()
}

// titleAlign将会影响标题栏所有行的对齐方式
export function setDefaultTitleAlign(alignment: string) {
    console.log(`setDefaultTitleAlign: ${alignment}`);
    ActiveObserver.instance.defaultTitleAlign.value = alignment
}


/**
 * 更新标题编辑器和内容编辑器的选区位置
 * @param {Editor} titleEditor 标题编辑器
 * @param {Editor} contentEditor 内容编辑器
 * @param {Editor} coordsJson 坐标信息
 * @returns
 */
export function focusCoordinate(
    titleEditor: Editor,
    contentEditor: Editor,
    coordsJson: string
) {
    const coords: Coordinate = JSON.parse(coordsJson);
    const posInfo = contentEditor.view.posAtCoords({
        left: coords.x,
        top: coords.y,
    });
    // 通过Editor.view.posAtCoords方法是否找到对应的editor
    if (posInfo) {
        // 如果拖拽到内容区域
        let { x, y } = coords;
        const { pos } = posInfo;
        if (coords.textRestricted) {
            if (contentEditor.state.doc.resolve(pos).node(1)) {
                contentEditor.commands.focusNoScroll(pos);
            } else {
                let element = document.elementFromPoint(
                    coords.x,
                    coords.y
                ) as HTMLElement;
                const viewDom = contentEditor.view.dom;
                if (element === viewDom) {
                    while (true) {
                        const _pos = contentEditor.view.posAtCoords({
                            left: x,
                            top: (y -= 10),
                        })?.pos;
                        if (!_pos) return;
                        if (contentEditor.state.doc.resolve(_pos).node(1))
                            return contentEditor.commands.focusNoScroll(_pos);
                    }
                } else if (element) {
                    if (element.tagName === "P") return contentEditor.commands.focusNoScroll(pos);
                    // 如果拖拽到附件左右两侧光标元素上，直接focus
                    if (element.getAttribute("data-pos")) return element.focus({ preventScroll: true });
                    // 如果拖拽到附件上 x 坐标小于附件宽度的一半，则光标在附件左侧，否则在附件右侧
                    while (true) {
                        if (element.parentElement === viewDom) {
                            const { before, after } =
                                getReferenceCursorElementByAnyone(element);
                            const offsetLeft = element.offsetLeft;
                            const clientWidth = element.clientWidth;
                            if (x < clientWidth / 2 + offsetLeft) {
                                document.activeElement !== before && before.focus({ preventScroll: true });
                            } else {
                                document.activeElement !== after && after.focus({ preventScroll: true });
                            }

                            return;
                        }
                        // 如果拖拽到底部bottomPlaceholder元素上，直接focus末尾
                        if (element.parentElement === document.documentElement) return contentEditor.commands.focusNoScroll("end");
                        element = element.parentElement;
                    }
                }
            }
        } else {
            contentEditor.commands.focusNoScroll(pos);
        }
    } else {
        // 否则拖拽到标题区域
        const posInTitle = titleEditor.view.posAtCoords({
            left: coords.x,
            top: coords.y,
        });
        console.log(`focusCoordinate: title editor's pos = ${posInTitle?.pos}`);
        if (posInTitle != undefined) {
            if (coords.textRestricted) {
                titleEditor.commands.focusNoScroll(posInTitle.pos);
            }
        }
    }
}

export function setUiMode(titleEditor: Editor, contentEditor: Editor, uiMode: string) {
    console.log(`setUiMode: ${uiMode}`)
    ActiveObserver.instance.editorUiMode.value = uiMode
    switch (uiMode) {
        case "1": //浏览模式
        case "2": { //叠图模式
            ActiveObserver.instance.linkColor.value = ActiveObserver.instance.defaultLinkColor.value
            ActiveObserver.instance.linkColorInBlockquote.value = ActiveObserver.instance.defaultLinkColor.value
            ActiveObserver.instance.textDecoration.value = "underline"
            ActiveObserver.instance.linkMarkerDisplay.value = "inline-flex"
            if (contentEditor.state.selection.empty) {
                updateDotMenuVisibility()
            }
            break
        }
        //编辑模式
        case "3":
        default: {
            ActiveObserver.instance.linkColor.value = ActiveObserver.instance.pColor.value
            ActiveObserver.instance.linkColorInBlockquote.value = ActiveObserver.instance.pColor54.value
            ActiveObserver.instance.textDecoration.value = "none"
            ActiveObserver.instance.linkMarkerDisplay.value = "none"
            if (contentEditor.state.selection.empty) {
                updateDotMenuVisibility(true)
            }
            break
        }
    }
    const isViewMode = uiMode === '1'
    const isOverlayMode = uiMode === '2'
    const isEditMode = uiMode === '3'
    const tiptapContentParent = queryTiptapContentParentElement()
    if (tiptapContentParent) {
        if (isOverlayMode) {
            tiptapContentParent.classList.add('tiptapContentParentOverlayMode')
        } else {
            /*
            退出叠图模式时需要先补空行，然后才能remove tiptapContentParentOverlayMode.
            否则会出现remove tiptapContentParentOverlayMode导致高度计算方式变更，页面出现滚动跳变的情况
            */
            if (!DEBUG_ON_WEB) {
                if (tiptapContentParent.classList.contains('tiptapContentParentOverlayMode')) {
                    // 判断包含tiptapContentParentOverlayMode时才执行补空行操作，减少与native端接口调用频次
                    const paintHeight = (window as any).injectedObject?.getOverlayPaintHeight()
                    if (paintHeight) {
                        addEmptyLinesForPaint(titleEditor, contentEditor, paintHeight)
                    }
                }
            }
            tiptapContentParent.classList.remove('tiptapContentParentOverlayMode')
        }
        if (isEditMode) {
            tiptapContentParent.classList.add('tiptapContentParentEditMode')
        } else {
            tiptapContentParent.classList.remove('tiptapContentParentEditMode')
        }
        if (isViewMode) {
            tiptapContentParent.classList.add('tiptapContentParentViewMode')
        } else {
            tiptapContentParent.classList.remove('tiptapContentParentViewMode')
        }
    }
}

export function deleteNodeByAttachId(editor: Editor, attachId: string) {
    console.log(`deleteNodeByAttachId: attachId = ${attachId}`)
    const chain = editor.chain()
    editor.state.doc.descendants((node, pos) => {
        if (node.attrs.attachid === attachId) {
            const range = { from: pos, to: pos + node.nodeSize }
            Log.d(`Tiptap`, `deleteNodeByAttachId: find target ${attachId}, delete range ${JSON.stringify(range)}`, true)
            chain
                .command(({ commands }) => {
                    // 如果删除的区域是整个文档, 就将区域内的内容替换成空的p标签(文档内至少保留一个空行)
                    if(range.from === 0 && range.to === editor.state.doc.content.size) {
                        return commands.insertContentAt(range, '<p></p>')
                    } else {
                        return commands.deleteRange(range)
                    }
                })
                .command(({ state, tr }) => {
                    // 被删除的附件后面如果跟的是列表项，则尝试合并列表项
                    const list = findParentNode(node => isListType(state, node.type.name))(new TextSelection(tr.selection.$from))
                    if (list) {
                        const listTypeOrName = list.node.type.name
                        const listType = getNodeType(listTypeOrName, editor.view.state.schema)
                        Log.d(`Tiptap`, `deleteNodeByAttachId: find parent node:${listType.name}, so joinList.`, true)
                        return joinListBackwards(tr, listType) && joinListForwards(tr, listType)
                    } else {
                        return true
                    }
                })
        }
    })
    chain.run()
}

export function getNodeRectByCoords(editor: Editor, coordsJson: string): string {
    const coords: Coordinate = JSON.parse(coordsJson)
    const pos = editor.view.posAtCoords({ left: coords.x, top: coords.y });
    if (pos != null) {
        const coord = editor.view.coordsAtPos(pos.pos)
        console.log(`getNodeRectByCoords: coords = [${coord.left}-${coord.top}-${coord.right}-${coord.bottom}],`);
        const rect: Rect = {
            left: coord.left,
            top: coord.top,
            right: coord.right,
            bottom: coord.bottom
        }
        return JSON.stringify(rect)
    }
    return ""
}

function getImageRectBySrc(editor: Editor, src: string): string {
    let rect = null
    editor.view.state.doc.forEach((node, offset, index): void => {
        const { type, attrs } = node
        if (type.name === 'image' && attrs.src.includes(src)) {
            console.log(`getImageRectBySrc: find target, size = ${node.nodeSize}`)
            const coord = editor.view.coordsAtPos(offset, node.nodeSize)
            console.log(`getImageRectBySrc: coords = [${coord.left}-${coord.top}-${coord.right}-${coord.bottom}],`);
            rect = {
                left: coord.left,
                top: coord.top,
                right: coord.right,
                bottom: coord.bottom
            }
            return

        }
    })
    if (rect != null) {
        return JSON.stringify(rect)
    } else {
        return ""
    }
}

function createFontStyle(fontName: string, fontPath: string): HTMLStyleElement {
    // 使用Document对象的createElement方法来创建一个<style>标签
    const styleTag = document.createElement("style");
    // 使用Text对象的textContent属性来设置<style>标签的内容，并编写@font-face规则
    styleTag.textContent = `
      @font-face {
        font-family: '${fontName}';
        src: url('${fontPath}');
      }
    `;
    return styleTag
}

export function insertCard(editor: Editor, attrJson: string) {
    setTimeout(() => {
    console.log(`insertCard: attr=${attrJson}`)
    const attachData: AttachData = JSON.parse(attrJson)
    const attr: CardAttr = attachData.card
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertCard(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
    }, 25)
}

function insertContactCard(editor: Editor, attrJson: string) {
    setTimeout(() => {
    const attachData: AttachData = JSON.parse(attrJson)
    const attr: ContactCardAttr = attachData.contactCard
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertContactCard(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
    }, 25)
}

function insertScheduleCard(editor: Editor, attrJson: string) {
    setTimeout(() => {
    const attachData: AttachData = JSON.parse(attrJson)
    const attr: ScheduleCardAttr = attachData.scheduleCard
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertScheduleCard(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
    }, 25)
}

function insertRecordCard(editor: Editor, attrJson: string) {
    setTimeout(() => {
    const attachData: AttachData = JSON.parse(attrJson)
    const attr: RecordAttr = attachData.record
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertRecord(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
    }, 25)
}

function insertFileCard(editor: Editor, attrJson: string) {
    setTimeout(() => {
        const attachData: AttachData = JSON.parse(attrJson)
        const attr: FileCardData = attachData.fileCard
        editor.chain().command(({commands}) => {
            if (!isEditMode() && attachData.insertToEndInNonEditMode) {
                commands.focus('end')
            }
            return true
        }).insertFileCard(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run()
    }, 25)
}
function updateFileDocThumbnail(editor: Editor, jsonString: string) {
    const params: FileCardData = JSON.parse(jsonString)
    console.log('updateFileDocThumbnail',params);
    editor.commands.updateFileDocThumbnail(params.attachId, params.docThumbnail)
}
function updateFileName(editor: Editor, jsonString: string) {
    const params: FileCardData = JSON.parse(jsonString)
    console.log('updateFileName',params);
    editor.commands.updateFileName(params.attachId, params.fileName)
}

function setTextSelection(editor: Editor, posJson: string) {
    console.log(`setTextSelection: ${posJson}`)
    const range: Range = JSON.parse(posJson)
    editor.commands.setTextSelection(range)
}

function setTextSelectionAll(editor: Editor) {
    const range = document.createRange()
    range.selectNodeContents(editor.view.dom)
    const selection = window.getSelection()
    selection.removeAllRanges()
    selection.addRange(range)
}

function cancelTextSelectionAll(editor: Editor) {
    const selection = window.getSelection()
    selection.removeAllRanges();
}

/** 添加流式生成组件 */
function insertSummaryStreamTip(editor: Editor, attrJson: string) {
    const $summaryStreamTip = editor.$node(SUMMARY_STREAM_TIPS_NODE_NAME)
    if(!$summaryStreamTip){
        const summaryStreamTipParas = JSON.parse(attrJson)
        editor.commands.insertSummaryStreamTip(summaryStreamTipParas)
    }
}

/** 添加文字 */
function insertSummaryContent(editor: Editor, attrJson: string) {
    const contentParas: PickedInputContent = JSON.parse(attrJson)
    editor.commands.insertSummaryContent(contentParas)
}
/** 更新流式生成组件 */
function updateSummaryStreamTip(editor: Editor, attrJson: string) {
    const summaryStreamTipParas = JSON.parse(attrJson)
    editor.commands.updateSummaryStreamTip(summaryStreamTipParas)
}

/** 删除流式生成组件 */
function deleteSummaryStreamTip(editor: Editor, attrJson: string) {
    editor.commands.deleteSummaryStreamTip()
}
/**拦截编辑点击事件 */
function interceptEditorClickEvent(editor:Editor,attrString:string){
    ActiveObserver.instance.isInterceptEditorClick.value = attrString
}
/**
 * Search for a possible draggable item upon an event that can initialize a drag operation.
 * Can be overridden in polyfill config.
 */
export function tryFindDraggableTarget(event: TouchEvent): HTMLElement | undefined {
    const cp = event.composedPath();
    for (const o of cp) {
        let el = <HTMLElement>o;
        do {
            if (el.draggable === false) {
                continue;
            }
            if (el.draggable === true && (el.tagName !== "IMG" && el.className !== "cover")) {
                //(el.tagName !== "IMG" && el.className !== "cover")规避视频元素抓取不准的问题
                return el;
            }
            if (el.getAttribute
                && el.getAttribute("draggable") === "true") {
                return el;
            }
        } while ((el = <HTMLElement>el.parentNode) && el !== document.body);
    }
}

export function getHTMLContentBetween(
    editor: Editor,
    from: number,
    to: number
): string {
    const { state } = editor
    const nodesArray: string[] = []

    state.doc.nodesBetween(from, to, (node, pos, parent) => {
        if (parent === state.doc) {
            const serializer = DOMSerializer.fromSchema(editor.schema)
            const dom = serializer.serializeNode(node)
            const tempDiv = document.createElement('div')
            tempDiv.appendChild(dom)
            nodesArray.push(tempDiv.innerHTML)
        }
    })

    return nodesArray.join('')
}

export function findCardButtonElement(event: TouchEvent): HTMLElement | undefined {
    const eventTarget = event.target as HTMLElement
    const type = eventTarget.getAttribute('type')
    if (eventTarget.nodeName == 'BUTTON' && type == 'cardbutton') {
        return eventTarget
    }

    return undefined
}

export function isStreamAction(
  event: TouchEvent
): boolean {
  const eventTarget = event.target as HTMLElement;
  return eventTarget.classList.contains("streaming-action")
}

function setRecordCurrentTime(editor: Editor, jsonString: string) {
    const recordAttr: RecordAttr = JSON.parse(jsonString)
    editor.commands.setRecordCurrentTime(recordAttr.attachId, recordAttr.currentTime, recordAttr.duration)
}

function updateRecordState(editor: Editor, jsonString: string) {
    console.log(`updateRecordState: jsonString=${jsonString}`)
    const recordAttr: RecordAttr = JSON.parse(jsonString)
    editor.commands.updateRecordPlayState(recordAttr.attachId, recordAttr.state)
}

function updateRecordCallLogs(editor: Editor, jsonString: string) {
    console.log(`updateRecordState: jsonString=${jsonString}`)
    const recordAttr: RecordAttr = JSON.parse(jsonString)
    editor.commands.updateRecordCallLogs(recordAttr.attachId, recordAttr.hasCallLogs)
}

function updateRecord(editor: Editor, jsonString: string) {
    console.log(`updateRecord: jsonString=${jsonString}`)
    const recordAttr = JSON.parse(jsonString)
    editor.commands.updateRecord(recordAttr.oldAttachId, recordAttr.src, recordAttr.attachId, recordAttr.recordId)
}

function disableRecord(editor: Editor, jsonString: string) {
    console.log(`disableAudioRecord: jsonString=${jsonString}`)
    const recordAttr = JSON.parse(jsonString)
    editor.commands.disableRecord(recordAttr.attachId, recordAttr.disable)
}

export function domToImage(editor: Editor): string {
    console.log(`domToImage:`)
    const app = document.getElementById('app')
    let base64Url = ""
    if (app) {
        domtoimage.toSvg(app)
            .then(function (dataUrl) {
                base64Url = dataUrl
                const img = document.createElement('img')
                img.setAttribute('src', dataUrl)
                const devicePixelRatio = window.devicePixelRatio
                img.onload = function () {
                    const canvas = document.createElement('canvas')
                    const content = canvas.getContext('2d')
                    canvas.width = img.width * devicePixelRatio
                    canvas.height = img.height * devicePixelRatio
                    if (content) {
                        content.drawImage(
                            img,
                            0,
                            0,
                            img.width * devicePixelRatio,
                            img.height * devicePixelRatio
                        )
                        content.imageSmoothingEnabled = false
                        /* content.mozImageSmoothingEnabled = false
                        content.webkitImageSmoothingEnabled = false
                        content.msImageSmoothingEnabled = false */
                    }
                    base64Url = canvas.toDataURL('image/png')
                }
            });
    }
    return base64Url
}

function getSelectionHtml(editor:Editor,isNeedForward: Boolean): string  {
    const { state } = editor;
    const { selection, tr } = state;
    const serializer = DOMSerializer.fromSchema(state.schema);
    const div = document.createElement('div');
    let contentToSerialize;
    if(selection.empty){
        if(isNeedForward){
            const from = 0;
            const to = selection.from;
            const newSelection = TextSelection.create(state.doc, from, to);
            contentToSerialize = newSelection.content().content;
        }else{
            return ""
        }
    }else{
        contentToSerialize = selection.content().content;
    }
    div.appendChild(serializer.serializeFragment(contentToSerialize));
    return div.innerHTML
}

function setSummaryEntity(editor: Editor, entity: string) {
    const summaryEntity = JSON.parse(entity)
    editor.chain().setSummaryEntity(summaryEntity.entities, summaryEntity.marks)
        .setMeta("addToHistory", false)
        .setMeta("updateSummaryLink", true).run()
}

export function search(titleEditor: Editor, contentEditor: Editor, searchParaStr: string) {
    console.log(`search: ${searchParaStr}`)
    const searchPara = JSON.parse(searchParaStr)
    const searchList = searchPara.searchList
    const isFromSearchNoteList = searchPara.isFromSearchNoteList
    titleEditor.commands.setSearchTerm(searchList, isFromSearchNoteList);
    contentEditor.commands.setSearchTerm(searchList, isFromSearchNoteList);
    const titleResult: SearchMatchedRange[] = titleEditor.storage.searchAndReplace.results
    const ContentResult: SearchMatchedRange[] = contentEditor.storage.searchAndReplace.results
    const result = {
        ranges: titleResult.concat(ContentResult),
        index: 0
    }
    if (searchPara.highLightFirstOne && searchPara.scrollToFirstOne) {
        //详情页点击查找进行的搜索。否则是列表搜索然后进入的详情页
        ActiveObserver.instance.searchResult.value = result
    }
    const attachId = searchPara.attachId
    if(!attachId){
        if (result.ranges.length > 0) {
            Log.d(`Tiptap`, `search: find result length=${result.ranges.length}`)
            console.log(`search: find result length=${result.ranges.length}, result=${JSON.stringify(result)}`)
            const firstResult = result.ranges[0]
            const { fromEditor, to, index, attachmentId} = firstResult
            const editor = (fromEditor == Constants.TITLE_EDITOR) ? titleEditor : contentEditor
            if (isFromSearchNoteList) {
                if (searchPara.scrollToFirstOne) {
                    setTimeout(() => {
                        if (attachmentId) {
                            scrollToAttachment(attachmentId, true)
                        } else {
                            const searchResult = document.querySelectorAll('.search-result')

                            const searchKey = searchList[0]
                            let smoothIndex = 0
                            for (let i = 0; i < searchResult.length; i += 1) {
                                if (searchKey == (searchResult[i] as HTMLElement).innerText) {
                                    smoothIndex = i
                                    break
                                }
                            }
                            const target = searchResult[smoothIndex]
                            if (target) {
                                const closestTable = target.closest('table')
                                if (closestTable) {
                                    const attachId = closestTable.getAttribute('attachid')
                                    console.log(`search: target in table, attachId=${attachId}`)
                                    const bs = ScrollHelper.getScroll(attachId)
                                    bs?.scroller.hooks.once('scrollEnd', () => { target.scrollIntoView({ behavior: "smooth", block: "center" }) })
                                    bs?.scroller.hooks.once('scrollCancelForSamePoint', () => { target.scrollIntoView({ behavior: "smooth", block: "center" }) })
                                    bs?.scrollToElement(target as HTMLElement, 500, true, true)
                                } else {
                                    target.scrollIntoView({ behavior: "smooth", block: "center" })
                                }
                            }
                        }
                    })
                }
            } else {
                if (searchPara.highLightFirstOne && searchPara.scrollToFirstOne) {
                    if (attachmentId) {
                        editor.commands.swicthResultIndex(index)
                        scrollToAttachment(attachmentId, false)
                    } else  {
                        editor.chain().swicthResultIndex(index).focus(to).scrollIntoView().blur().run()
                    }
                } else if (searchPara.scrollToFirstOne) {
                    if (attachmentId) {
                        editor.commands.swicthResultIndex(index)
                        scrollToAttachment(attachmentId, false)
                    } else  {
                        editor.chain().swicthResultIndex(-1).focus(to).scrollIntoView().blur().run()
                    }
                }
            }
        }
    }else{
        scrollToAttachment(attachId, true)
    }
}

function scrollToAttachment(attachId: string, smoothScroll: boolean) {
    const searchResult = document.querySelectorAll(`[attachid="${attachId}"]`)
    if (smoothScroll) {
        searchResult[0] && searchResult[0].scrollIntoView({ behavior: "smooth", block: "center" })
    } else  {
        searchResult[0] && searchResult[0].scrollIntoView({  block: "center" })
    }
}

export function matchPrevious(titleEditor: Editor, contentEditor: Editor) {
    console.log(`matchPrevious:`)
    const result = ActiveObserver.instance.searchResult.value
    if (result) {
        const targetIndex = (result.index - 1 < 0) ? 0 : --result.index
        const { fromEditor, to, index, attachmentId} = result.ranges[targetIndex]
        Log.d(`Tiptap`, `previousMatch: ${targetIndex}, fromEditor=${fromEditor}, to=${to}, innerIndex=${index}`, true)
        // 先重置resultIndex
        titleEditor.commands.resetResultIndex()
        contentEditor.commands.resetResultIndex()
        const editor = (fromEditor == Constants.TITLE_EDITOR) ? titleEditor : contentEditor
        editor.commands.swicthResultIndex(index)
        if (attachmentId) {
            scrollToAttachment(attachmentId, false)
        } else  {
            // focus函数默认自动滚动
            editor.commands.focus(to)
        }
        ActiveObserver.instance.searchResult.value = {
            ranges: result.ranges,
            index: targetIndex,
        }
    }
}

export function matchNext(titleEditor: Editor, contentEditor: Editor) {
    console.log(`matchNext:`)
    const result = ActiveObserver.instance.searchResult.value
    if (result) {
        const targetIndex = (result.index + 1 >= result.ranges.length)
            ? result.ranges.length - 1 : ++result.index
        const { fromEditor, to, index, attachmentId} = result.ranges[targetIndex]
        Log.d(`Tiptap`, `nextMatch: ${targetIndex}, fromEditor=${fromEditor}, to=${to}, innerIndex=${index}`, true)
        // 先重置resultIndex
        titleEditor.commands.resetResultIndex()
        contentEditor.commands.resetResultIndex()
        const editor = (fromEditor == Constants.TITLE_EDITOR) ? titleEditor : contentEditor
        editor.commands.swicthResultIndex(index);
        if (attachmentId) {
            scrollToAttachment(attachmentId, false)
        } else {
            // focus函数默认自动滚动
            editor.commands.focus(to)
        }
        ActiveObserver.instance.searchResult.value = {
            ranges: result.ranges,
            index: targetIndex,
        }
    }
}

export function clearSearchResult(titleEditor: Editor, contentEditor: Editor) {
    console.log(`clearSearchResult:`)
    const para = {
        searchList: [],
        attachId:"",
        highLightFirstOne: true,
        scrollToFirstOne: true
    }
    search(titleEditor, contentEditor, JSON.stringify(para))
}

export function setEditorScale(scale: number) {
    console.log(`setEditorScale: ${scale}`)
    ActiveObserver.instance.editorScale.value = scale
}

function insertPaint(editor: Editor, paintJson: string) {
    console.log(`insertPaint: ${paintJson}`);
    const attachData: AttachData = JSON.parse(paintJson);
    const attr: PaintAttr = attachData.paint;
    editor.chain().command(({ commands }) => {
        if (!isEditMode() && attachData.insertToEndInNonEditMode) {
            commands.focus('end')
        }
        return true
    }).insertPaint(attr).setMeta("addToPrevGroup", attachData.addToPrevGroup).run();
}

function updatePaint(editor: Editor, paintJson: string) {
    console.log(`updatePaint: ${paintJson}`);
    const attr: UpdatePaintAttr = JSON.parse(paintJson);
    const oldPaintId = attr.oldPaintId
    const newPaintAttr = attr.newAttr
    editor.commands.updatePaint(oldPaintId, newPaintAttr)
}

function deletePaint(editor: Editor, paintId: string) {
    console.log(`deletePaint: ${paintId}`);
    editor.commands.deletePaint(paintId)
}

function addEmptyLinesForPaint(titleEditor: Editor, contentEditor: Editor, paintHeight: number) {
    const titleHeight = getEditorHeight(titleEditor, true)
    const contentHeight = getEditorHeight(contentEditor, false)
    const totalHeight = titleHeight + contentHeight
    // 为了确保在叠涂插入后，能正常在叠涂底下点击添加文字，文本内容高度至少要比叠涂高度高出一行
    if (paintHeight > totalHeight) {
        const singleLineTextHeight = getSingleLineTextHeight(contentEditor)
        const addLineCount = Math.ceil((paintHeight - totalHeight) / singleLineTextHeight)
        Log.d(`EditorUtils`, `addEmptyLinesForPaint, paintHeight:${paintHeight}, totalHeight:${totalHeight}, singleLineTextHeight:${singleLineTextHeight}, addLineCount:${addLineCount}`);
        addEmptyLinesAtEnd(contentEditor, addLineCount)
    }
}

export function getPlaceholderWidth(editor:Editor): number {
    try {
        // 获取编辑器内容的样式。可以选择任何一个在编辑器中的文本段落。
        const element = editor.view.dom.querySelector('h1')
        if (!element) {
            console.log(`getPlaceholderWidth, can not found valid h1 element`)
            const realRemSize = getRealRemToPxSize()
            return remToPxWithRemRadio(ActiveObserver.instance.h1LineHeight.value, realRemSize)
        }
        // 创建一个用于计算的隐藏元素
        const measuringElement = document.createElement('div');

        // 复制编辑器文本的相关样式到计算元素
        const style = getComputedStyle(element)
        measuringElement.style.fontFamily = style.fontFamily;
        measuringElement.style.fontSize = style.fontSize;
        measuringElement.style.fontWeight = style.fontWeight;
        measuringElement.style.letterSpacing = style.letterSpacing;
        // 应用其他可能影响高度的样式，例如行高、边距、填充等
        measuringElement.style.lineHeight = style.lineHeight;

        // 确保新元素不会实际显示在页面上
        measuringElement.style.position = 'absolute';
        measuringElement.style.visibility = 'hidden';
        measuringElement.style.whiteSpace = 'nowrap';  // 确保文本不会换行
        measuringElement.style.width = 'fit-content';
        measuringElement.style.height = 'auto';

        // 为测量元素添加文本
        let placeHolderText = ActiveObserver.instance.defaultTitleName.value
        if (placeHolderText.length == 0) {
            placeHolderText = "标题"
        }
        measuringElement.textContent = placeHolderText;

        // 插入的元素需要加入到DOM树中才能正确地进行尺寸测量
        document.body.appendChild(measuringElement);
        // 计算测量元素的高度
        const width = measuringElement.offsetWidth;  // 或者 getBoundingClientRect().height

        // 清理：从DOM树中移除测量用的元素
        document.body.removeChild(measuringElement);

        console.log('getPlaceholderWidth:', width);
        return width
    } catch (error) {
        Log.e('Tiptap', `getPlaceholderWidth: error=${error}`, true)
        return 0
    }
}

export function getSingleLineTextHeight(editor: Editor): number {
    // 获取编辑器中P节点元素
    let element = editor.view.dom.querySelector(':not(blockquote) > p');
    if (!element) {
        console.log(`getSingleLineTextHeight, can not found valid p element`)
        const realRemSize = getRealRemToPxSize()
        return remToPxWithRemRadio(ActiveObserver.instance.pLineHeight.value, realRemSize)
    }

    // 创建一个用于计算的隐藏元素
    const measuringElement = document.createElement('div');
    // 复制编辑器文本的相关样式到计算元素
    const style = getComputedStyle(element)
    measuringElement.style.fontFamily = style.fontFamily;
    measuringElement.style.fontSize = style.fontSize;
    measuringElement.style.fontWeight = style.fontWeight;
    measuringElement.style.letterSpacing = style.letterSpacing;
    // 应用其他可能影响高度的样式，例如行高、边距、填充等
    measuringElement.style.lineHeight = style.lineHeight;

    // 确保新元素不会实际显示在页面上
    measuringElement.style.position = 'absolute';
    measuringElement.style.visibility = 'hidden';
    measuringElement.style.whiteSpace = 'nowrap';  // 确保文本不会换行
    measuringElement.style.width = 'auto';
    measuringElement.style.height = 'auto';

    // 为测量元素添加文本
    measuringElement.textContent = 'Test 测量';

    // 插入的元素需要加入到DOM树中才能正确地进行尺寸测量
    document.body.appendChild(measuringElement);
    // 计算测量元素的高度
    const height = measuringElement.offsetHeight;  // 或者 getBoundingClientRect().height

    // 清理：从DOM树中移除测量用的元素
    document.body.removeChild(measuringElement);

    console.log('单行文本的高度:', height);
    return height
}

function addEmptyLinesAtEnd(editor: Editor, lineCount: number) {
    const emptyLinesContent = '<p></p>'.repeat(lineCount)
    const endPosition = editor.state.doc.content.size
    editor.chain().setMeta("addToHistory", false).insertContentAt(endPosition, emptyLinesContent).run()
}

/**
 * 获取根节点中的font-size值，用于缩放后rem转换为px时比例计算
 * @returns
 */
function getRealRemToPxSize(): number {
    const fontSize = window.getComputedStyle(document.documentElement)['font-size']
    let remSize = 16 // 默认值1rem = 16px
    if (fontSize) {
        if (typeof (fontSize) === "number") {
            remSize = fontSize
        } else {
            const numStr = fontSize.toString().replace(/(.*?)px/gi, (_, p1) => {
                return p1
            })
            remSize = Number(numStr)
        }
    }
    return remSize
}

export function updateAttr(editor: Editor, pos: number, attrs: Attrs) {
    console.log(`updateAttr: pos=${pos}, attrs=${JSON.stringify(attrs)}`)
    const transaction = editor.state.tr.setNodeMarkup(pos, null, attrs)
    editor.view.dispatch(transaction)
}

export function insertHintText(editor: Editor, text: string) {
    if (text === "") {
        editor
            .chain()
            .focus()
            .command(({ commands, tr }) => {
                commands.deleteSelection()
                tr.setMeta("addToHistory", false)
                return true
            })
            .run()
    } else {
        console.log(`insertHintText: active-element`, document.activeElement)
        const { dataPos: pos, anchor } = getCursorInfo()
        // 如果光标当前在附件左右的话, 就先将光标更新到附件的上一行或者下一行
        if(anchor) {
            let index = getIndexByElement(anchor.parentElement)
            let { node: tiptapNode, position, preNode, nextNode } = getNodeInfoByIndex(editor, index)
            if (!tiptapNode) return console.log('attach-log-input-beforeinput-获取目标节点失败', index)
            if (pos === 'before') {
              if ((preNode && isAttachNode(preNode)) || index === 0) {
                let _pos = index === 0 ? 0 : position - 1
                // 如果光标在附件的前面,且附件是第一个元素, 就往前插入一个空行
                editor.chain().insertContentAt(_pos, `<p></p>`).focus(_pos + 1).run()
              } else {
                editor.commands.focus(position - 2)
              }
            } else {
                editor.commands.focus(isAttachNode(nextNode) ? position : position + 1)
            }
        }

        const state = editor.state
        const { $from, $to } = state.selection
        console.log(`insertHintText: text=${text}, from=${$from.pos}, to=${$to.pos}`)
        editor
            .chain()
            .focus()
            .command(({ tr, chain }) => {
                chain()
                    .insertContent(text)
                    .setTextSelection({ from: $from.pos, to: $from.pos + text.length })
                tr.setMeta("addToHistory", false)
                return true
            })
            .focus()
            .run()
    }
}

export function insertPhoneHintText(editor: Editor, text: string) {
    console.log(`insertPhoneHintText: text=${text}`)
    editor
        .chain()
        .focus()
        .command(({ tr, commands }) => {
            const html = `<CustomPhoneHint>${text}</CustomPhoneHint><p></p>`
            commands.insertContent(html)
            tr.setMeta("addToHistory", false)
            return true
        })
        .run()
}

export function drawDoodle(editor: Editor) {
    console.log(`draw doodle`)
    editor
        .chain()
        .command(({ tr }) => {
            tr.step(new AttrStep(0, "doodle", true))
            tr.setMeta("doodle", true)
            return true
        })
        .run()
}

export function notifyContentEditorTitleChanged(editor: Editor) {
    console.log(`notifyContentEditorTitleChanged`)
    editor
        .chain()
        .command(({ tr }) => {
            tr.step(new AttrStep(0, ATTR_MODIFY_TITLE, true))
            return true
        })
        .run()
}

export function getAllAttachments(editor: Editor): string {
    console.log(`getAllAttachments`)
    const tempArray: string[] = []
    editor.state.doc.descendants((node, pos) => {
        if (isAttachNode(node)) {
            if (node.attrs.attachid) {
                tempArray.push(node.attrs.attachid)
            }
        }
    })
    return tempArray.join(',')
}

export function getSelectionInfo(editor: Editor): string {
    const selection = editor.state.selection
    const result = {
        empty: selection.empty,
        from: selection.from,
        to: selection.to,
        isInTitle: isHeadingNode(selection.$to?.parent),
        inInTable: isSelectionInTable2(editor.state),
        isCellSel: isCellSelection(editor.state.selection),
        isAttachNode: isNodeSelection(selection)
    }
    return JSON.stringify(result)
}

// 将选区信息上传给安卓端
export function updateSelectionInfo(editor: Editor) {
    if(!editor) return console.log('update-selection-info: editor 参数错误')
    if (!DEBUG_ON_WEB) {
        (window as any).injectedObject?.onSelection(getSelectionInfo(editor))
    }
}

export function getEditorHeight(editor: Editor, includeDomBottom: boolean): number {
    let totalHeight = 0
    const dom = editor.view.dom
    if (dom) {
        //先获取dom节点本身的上下padding和border
        let lastMarginBottom = 0
        const domStyle = window.getComputedStyle(dom);
        totalHeight += parseFloat(domStyle.paddingTop) + parseFloat(domStyle.borderTopWidth)
        if (includeDomBottom) {
            //获取标题和内容的总高度时，内容区的paddingBottom和borderBottom不应该计算进去，否则获取的数值对比webview.contentHeight会偏大
            totalHeight += parseFloat(domStyle.paddingBottom) + parseFloat(domStyle.borderBottomWidth)
        }
        //再获取children的高度
        let lastMarginTop = 0
        Array.from(dom.children).forEach(child => {
            const childStyle = window.getComputedStyle(child)
            let childHeight = 0
            if (childStyle.display !== "none" && !child.classList.contains(beforeClass) && !child.classList.contains(afterClass) && !child.classList.contains(NAME_SHOW_ROW_DOT_MENU)) {
                childHeight += parseFloat(childStyle.height) //height已包含padding
                childHeight += parseFloat(childStyle.borderTopWidth)
                childHeight += parseFloat(childStyle.borderBottomWidth)
                const marginTop = parseFloat(childStyle.marginTop)
                const marginBottom = parseFloat(childStyle.marginBottom)
                //两个元素中间的margin不会叠加，需要进行合并计算
                let combinedMargin
                if (lastMarginBottom < 0 && marginTop < 0) {
                    //连续两个外边距都小于0，取绝对值大的负值
                    combinedMargin = Math.min(lastMarginBottom, marginTop)
                } else if ((lastMarginBottom < 0 && marginTop >= 0) || (lastMarginBottom >= 0 && marginTop < 0)) {
                    //一个大于0，一个小于0，则取两者之和
                    combinedMargin = lastMarginBottom + marginTop
                } else {
                    //两个都大于0，取较大的值
                    combinedMargin = Math.max(marginTop, lastMarginBottom)
                }
                childHeight += combinedMargin
                totalHeight += childHeight
                lastMarginTop = marginTop
                lastMarginBottom = marginBottom
            }
        });
        //最后一个元素的marginbottom需要加上
        totalHeight += lastMarginBottom
    }
    return totalHeight
}

function setBasicCssParams(basicCssData: string) {
    const basicCssParams: BasicCssParams = JSON.parse(basicCssData);
    console.log(`setBasicCssParams: ${basicCssData}`);
    if (basicCssParams.dir != null) {
        document.documentElement.setAttribute('dir', basicCssParams.dir)
    }
    if (basicCssParams.titleFontWght != null) {
        ActiveObserver.instance.titleFontVariationSettings.value = `"wght" ${basicCssParams.titleFontWght}`
    }
    if (basicCssParams.contentFontWght != null) {
        ActiveObserver.instance.contentFontVariationSettings.value = `"wght" ${basicCssParams.contentFontWght}`;
    }
    if (basicCssParams.boldContentFontWght != null) {
        ActiveObserver.instance.boldFontWeight.value = basicCssParams.boldContentFontWght.toString()
        ActiveObserver.instance.boldFontVariationSettings.value = `"wght" ${basicCssParams.boldContentFontWght}`;
    }
    if (basicCssParams.linkColor != null) {
        ActiveObserver.instance.defaultLinkColor.value = basicCssParams.linkColor
    }
    if (basicCssParams.textThickness != null) {
        ActiveObserver.instance.textDecorationThickness.value = `${pxToRem(basicCssParams.textThickness)}`
    }
    if (basicCssParams.textColorHighlight != null) {
        ActiveObserver.instance.defaultTextColorHighLight.value = basicCssParams.textColorHighlight
    }
    if (basicCssParams.colorPrimary != null) {
        ActiveObserver.instance.colorPrimary.value = basicCssParams.colorPrimary
        initThemeIcons()
        ActiveObserver.instance.linkMarkerDrawable.value = getLinkMarkerSvgUrl(basicCssParams.colorPrimary)
        setBlockSelectedcolor()
    }
}
// 设置附件的选中色, 亮色模式下使用主题色+12的透明度, 暗色下使用#E5A10033,
export function setBlockSelectedcolor() {
    if(!ActiveObserver.instance) return console.log('ActiveObserver.instance 实例不存在')
    const { colorPrimary, aigcTextUiModeApplyCss, blockSelectedColor, darkBlockSelectedColor } = ActiveObserver.instance
    // 亮色模式下 使用卡片蒙层颜色为主题色+透明度12%  暗色模式使用 '#E5A10033'
    blockSelectedColor.value = aigcTextUiModeApplyCss.value === 0 ? hexToRgba(colorPrimary.value, 31) : darkBlockSelectedColor
    console.log('set-block-selected-color', blockSelectedColor.value, colorPrimary.value)
}

function setSkinCssParams(skinCssData: string) {
    const skinCssParams: SkinCssParams = JSON.parse(skinCssData);
    console.log(`setSkinCssParams: ${skinCssData}`);

    removeSelectionCellBorderWidget()
    const contentSkinCss = skinCssParams.contentSkinCss
    const tiptapContentEle = queryTiptapContentElement()
    if (contentSkinCss != null) {
        if (contentSkinCss.textColor != null) {
            ActiveObserver.instance.pColor.value = contentSkinCss.textColor
            ActiveObserver.instance.pColor26.value = hexToRgba(contentSkinCss.textColor, 67)
            ActiveObserver.instance.pColor54.value = hexToRgba(contentSkinCss.textColor, 138)
        }
        if (contentSkinCss.lineHeight != null) {
            ActiveObserver.instance.pLineHeight.value = pxToRem(contentSkinCss.lineHeight)
            ActiveObserver.instance.pH1LineHeight.value = pxToRem(1.3 * Number(contentSkinCss.lineHeight))
            ActiveObserver.instance.pH2LineHeight.value = pxToRem(1.15 * Number(contentSkinCss.lineHeight))
            ActiveObserver.instance.pH3LineHeight.value = pxToRem(Number(contentSkinCss.lineHeight))
            console.log(`bxx hSize: ${ActiveObserver.instance.pH1LineHeight.value},${ActiveObserver.instance.pH2LineHeight.value},${ActiveObserver.instance.pH3LineHeight.value}`)
            ActiveObserver.instance.pQuoteLineHeight.value = pxToRem(0.88 * Number(contentSkinCss.lineHeight))
        }
        if (contentSkinCss.paddingLeft != null) {
            if (ActiveObserver.instance.isPaintEmpty.value) {
                ActiveObserver.instance.contentPaddingLeft.value = Number(contentSkinCss.paddingLeft) +'px'
            } else  {
                ActiveObserver.instance.contentPaddingLeft.value = pxToRem(contentSkinCss.paddingLeft)
            }
            ActiveObserver.instance.paddingLeft.value = Number(contentSkinCss.paddingLeft)
            tiptapContentEle?.style?.setProperty('--table-padding-left', ActiveObserver.instance.contentPaddingLeft.value)
        }
        if (contentSkinCss.paddingTop != null) {
            ActiveObserver.instance.contentPaddingTop.value = pxToRem(contentSkinCss.paddingTop)
        }

        if (contentSkinCss.paddingRight != null) {
            if (ActiveObserver.instance.isPaintEmpty.value) {
                ActiveObserver.instance.contentPaddingRight.value = Number(contentSkinCss.paddingRight) +'px'
            } else  {
                ActiveObserver.instance.contentPaddingRight.value = pxToRem(contentSkinCss.paddingRight)
            }
            ActiveObserver.instance.paddingRight.value = Number(contentSkinCss.paddingRight)
            tiptapContentEle?.style?.setProperty('--table-padding-right', ActiveObserver.instance.contentPaddingRight.value)
        }

        if (contentSkinCss.paddingBottom != null) {
            ActiveObserver.instance.contentPaddingBottom.value = pxToRem(contentSkinCss.paddingBottom)
        }

        if (contentSkinCss.fontPath != null) {
            setContentFont(contentSkinCss.fontPath, skinCssParams.isSwitchToSystemFontFromThird)
        }
    }

    const titleSkinCss = skinCssParams.titleSkinCss
    if (titleSkinCss != null) {
        if (titleSkinCss.align != null) {
            setDefaultTitleAlign(titleSkinCss.align)
        }
        if (titleSkinCss.textColor != null) {
            ActiveObserver.instance.h1Color.value = titleSkinCss.textColor
        }
        if (titleSkinCss.hintTextColor != null) {
            ActiveObserver.instance.placeholderColor.value = titleSkinCss.hintTextColor
        }
        if (titleSkinCss.lineHeight != null) {
            ActiveObserver.instance.h1LineHeight.value = pxToRem(titleSkinCss.lineHeight)
        }
        if (titleSkinCss.paddingLeft != null) {
            if (ActiveObserver.instance.isPaintEmpty.value) {
                ActiveObserver.instance.titlePaddingLeft.value = Number(titleSkinCss.paddingLeft) +'px'
            } else  {
                ActiveObserver.instance.titlePaddingLeft.value = pxToRem(titleSkinCss.paddingLeft)
            }
        }
        if (titleSkinCss.paddingTop != null) {
            ActiveObserver.instance.titlePaddingTop.value = pxToRem(titleSkinCss.paddingTop)
        }
        if (titleSkinCss.paddingRight != null) {
            if (ActiveObserver.instance.isPaintEmpty.value) {
                ActiveObserver.instance.titlePaddingRight.value = Number(titleSkinCss.paddingRight) +'px'
            } else  {
                ActiveObserver.instance.titlePaddingRight.value = pxToRem(titleSkinCss.paddingRight)
            }
        }
        if (titleSkinCss.paddingBottom != null) {
            ActiveObserver.instance.titlePaddingBottom.value = pxToRem(titleSkinCss.paddingBottom)
        }
        if (titleSkinCss.fontPath != null) {
            setTitleFont(titleSkinCss.fontPath, skinCssParams.isSwitchToSystemFontFromThird)
        }
        setTitleBgSkin(titleSkinCss.bgType, titleSkinCss.bgValue)
    }

    const checkboxSkinCss = skinCssParams.checkBoxSkinCss
    if (checkboxSkinCss != null) {
        const uncheckSkinCss = checkboxSkinCss.uncheckedCss
        if (uncheckSkinCss != null) {
            const uncheckDrawable = uncheckSkinCss.drawableBase64Data
            if (uncheckDrawable != null) {
                // 将\n、空格替换为空字符， \/替换为/
                ActiveObserver.instance.unCheckDrawable.value =
                    getCheckBoxPngUrl(uncheckDrawable.replace(/\n/g, "")
                        .replace(/\\\//g, "/")
                        .replace(/ /g, ""))
            } else {
                const pathColor = uncheckSkinCss.pathColor
                if (pathColor != null) {
                    ActiveObserver.instance.unCheckDrawable.value = getUnCheckSvgUrl(pathColor.fillColor, pathColor.strokeColor)
                }
            }
        }

        const checkSkinCss = checkboxSkinCss.checkedCss
        if (checkSkinCss != null) {
            const checkDrawable = checkSkinCss.drawableBase64Data
            if (checkDrawable != null) {
                // 将\n、空格替换为空字符， \/替换为/
                ActiveObserver.instance.checkDrawable.value =
                    getCheckBoxPngUrl(checkDrawable.replace(/\n/g, "")
                        .replace(/\\\//g, "/")
                        .replace(/ /g, ""))
            } else {
                const pathColor = checkSkinCss.pathColor
                if (pathColor != null) {
                    ActiveObserver.instance.checkDrawable.value = getCheckSvgUrl(pathColor.fillColor, pathColor.strokeColor)
                }
            }
            if (checkSkinCss.textColor != null) {
                ActiveObserver.instance.pCheckColor.value = checkSkinCss.textColor
            }
        }
    }

    const webCardSkinCss = skinCssParams.webCardSkinCss
    if (webCardSkinCss != null) {
        if (webCardSkinCss.bgColor != null) {
            ActiveObserver.instance.cardBackGroundColor.value = webCardSkinCss.bgColor
        }
        if (webCardSkinCss.borderColor != null) {
            ActiveObserver.instance.cardBorderColor.value = webCardSkinCss.borderColor
        }
    }

    const combinedCardCss = skinCssParams.combinedCardCss
    if (combinedCardCss != null) {
        // backgroundColorChecked 通过主题色（colorPrimary）+12%透明度来计算  固不需要传递了
        // ActiveObserver.instance.backgroundColorChecked.value = combinedCardCss.backgroundColorChecked
        ActiveObserver.instance.backgroundColorUnChecked.value = combinedCardCss.backgroundColor
        ActiveObserver.instance.primaryTextColor.value = combinedCardCss.primaryTextColor
        ActiveObserver.instance.secondaryTextColor.value = combinedCardCss.secondaryTextColor
        ActiveObserver.instance.contactCopyTextColor.value = combinedCardCss.contactCopyTextColor
        ActiveObserver.instance.scheduleCopyTextColor.value = combinedCardCss.scheduleCopyTextColor
        ActiveObserver.instance.contactButtonBackColor.value = combinedCardCss.contactButtonBackColor
        ActiveObserver.instance.scheduleButtonBackColor.value = combinedCardCss.scheduleButtonBackColor
        ActiveObserver.instance.contactButtonTextColor.value = combinedCardCss.contactButtonTextColor
        ActiveObserver.instance.scheduleButtonTextColor.value = combinedCardCss.scheduleButtonTextColor
        const foreground = "#0000001F" //参照COUIButton的取值rgba(0, 0, 0, 0.12)
        ActiveObserver.instance.contactButtonPressBackColor.value = compositeColors(foreground, combinedCardCss.contactButtonBackColor)
        ActiveObserver.instance.scheduleButtonPressBackColor.value = compositeColors(foreground, combinedCardCss.scheduleButtonBackColor)
    }
    if (skinCssParams.backgroundColorCss != null) {
        ActiveObserver.instance.backgroundColorCss.value = skinCssParams.backgroundColorCss
    }
    if (skinCssParams.aigcTextUiModeApplyCss != null) {
        ActiveObserver.instance.aigcTextUiModeApplyCss.value = skinCssParams.aigcTextUiModeApplyCss
    }
    if (skinCssParams.tableCss != null) {
        tiptapContentEle?.style?.setProperty('--table-border-color', skinCssParams.tableCss.borderColor)
        tiptapContentEle?.style?.setProperty('--table-scroll-bar-color', skinCssParams.tableCss.scrollBarColor)
        tiptapContentEle?.style?.setProperty('--table-selected-color', skinCssParams.tableCss.selectedColor)
        tiptapContentEle?.style?.setProperty('--table-drag-background-color', hexToRgba(ActiveObserver.instance.backgroundColorCss.value, 204))
        const selectedMaskColor = compositeColors("#00000014", skinCssParams.tableCss.selectedColor)
        tiptapContentEle?.style?.setProperty('--table-selected-mask-color', selectedMaskColor)
        tiptapContentEle?.style?.setProperty('--table-background-color', skinCssParams.tableCss.backgroundColor == "" ? 'unset' : skinCssParams.tableCss.backgroundColor)
        const rowSvg = getRowDotSvgUrl(skinCssParams.tableCss.dotColor)
        const rowSelectedSvg = getRowDotSvgUrl(Constants.TABLE_DOT_MENU_SELECTED_COLOR)
        const columnSvg = getColumnDotSvgUrl(skinCssParams.tableCss.dotColor)
        const columnSelectedSvg = getColumnDotSvgUrl(Constants.TABLE_DOT_MENU_SELECTED_COLOR)
        const leftColHandleSvg = getLeftColHandleSvg(skinCssParams.tableCss.selectedColor)
        const topRowHandleSvg = getTopRowHandleSvg(skinCssParams.tableCss.selectedColor)
        const leftColHandleSelectedSvg = getLeftColHandleSvg(selectedMaskColor)
        const topRowHandleSelectedSvg = getTopRowHandleSvg(selectedMaskColor)
        tiptapContentEle?.style?.setProperty('--table-row-dot-svg', rowSvg)
        tiptapContentEle?.style?.setProperty('--table-column-dot-svg', columnSvg)
        tiptapContentEle?.style?.setProperty('--table-row-dot-selected-svg', rowSelectedSvg)
        tiptapContentEle?.style?.setProperty('--table-column-dot-selected-svg', columnSelectedSvg)
        tiptapContentEle?.style?.setProperty('--table-left-col-handle-svg', leftColHandleSvg)
        tiptapContentEle?.style?.setProperty('--table-top-row-handle-svg', topRowHandleSvg)
        tiptapContentEle?.style?.setProperty('--table-left-col-handle-selected-svg', leftColHandleSelectedSvg)
        tiptapContentEle?.style?.setProperty('--table-top-row-handle-selected-svg', topRowHandleSelectedSvg)
    }
}

export function setTitleBgSkin(type: string, value: string) {
    console.log(`setTitleBgSkin, type:${type}, value:${value}`)
    if (type == undefined || type == null || type == "" || value == undefined || value == null || value == "") {
        clearTitleBackground()
        clearTitleBorderImage()
        return
    }

    if (type == "1") {
        // 单图
        clearTitleBackground()
        readImageAndSlice(value).then(border => {
            if (border.sliceTop == -1 || border.sliceRight == -1 || border.sliceBottom == -1 || border.sliceLeft == -1) {
                // 没有配置拉伸区域，直接设置为背景图
                clearTitleBorderImage() //需清除border，否则两个会同时生效
                ActiveObserver.instance.titleBackground.value = `url(${border.imageData})`
                return
            }
            const borderSlice = `${border.sliceTop} ${border.sliceRight} ${border.sliceBottom} ${border.sliceLeft}`
            const borderWidth = `${border.sliceTop}px ${border.sliceRight}px ${border.sliceBottom}px ${border.sliceLeft}px`
            ActiveObserver.instance.titleBorderStyle.value = "dashed" // 随便指定一个支持的style，不为none就行。有配置图像时会优先显示图像
            console.log(`setTitleBgSkin: dataSize:${border.imageData.length}, slice:${borderSlice}, width:${borderWidth}`)
            ActiveObserver.instance.titleBorderImageSource.value = `url(${border.imageData})`
            ActiveObserver.instance.titleBorderImageSlice.value = `${borderSlice} fill`
            ActiveObserver.instance.titleBorderImageWidth.value = `${borderWidth}`
        }).catch((error) => {
            Log.e("TipTap", `setTitleBgSkin type:${type}, value:${value}, error:${error.message}`, true)
            clearTitleBackground()
            clearTitleBorderImage()
        })
    } else if (type == "2") {
        // 平铺
        clearTitleBorderImage()
        if (value.startsWith('http')) {
            ActiveObserver.instance.titleBackground.value = `url(${value})`
        } else {
            readImage(value).then(imgBase64 => {
                ActiveObserver.instance.titleBackground.value = `url(${imgBase64})`
            }).catch((error) => {
                Log.e("TipTap", `setTitleBgSkin type:${type}, value:${value}, error:${error.message}`, true)
            })
        }
    } else if (type == "border") {
        const borderImage: BorderImage = JSON.parse(value);
        readImage(borderImage.url).then(imgBase64 => {
            clearTitleBackground()
            const borderSlice = `${borderImage.sliceTop} ${borderImage.sliceRight} ${borderImage.sliceBottom} ${borderImage.sliceLeft}`
            const borderWidth = `${borderImage.sliceTop}px ${borderImage.sliceRight}px ${borderImage.sliceBottom}px ${borderImage.sliceLeft}px`
            ActiveObserver.instance.titleBorderStyle.value = "dashed" // 随便指定一个支持的style，不为none就行。有配置图像时会优先显示图像
            console.log(`setTitleBgSkin: dataSize:${imgBase64.length}, slice:${borderSlice}, width:${borderWidth}, repeat:${borderImage.repeat}`)
            ActiveObserver.instance.titleBorderImageSource.value = `url(${imgBase64})`
            ActiveObserver.instance.titleBorderImageSlice.value = `${borderSlice} fill`
            ActiveObserver.instance.titleBorderImageWidth.value = `${borderWidth}`
            ActiveObserver.instance.titleBorderImageRepeat.value = `${borderImage.repeat}`
        }).catch((error) => {
            Log.e("TipTap", `setTitleBgSkin type:${type}, value:${value}, error:${error.message}`, true)
            clearTitleBackground()
            clearTitleBorderImage()
        })
    } else {
        clearTitleBackground()
        clearTitleBorderImage()
    }
}

function clearTitleBackground() {
    ActiveObserver.instance.titleBackground.value = null
}

function clearTitleBorderImage() {
    ActiveObserver.instance.titleBorderImageSource.value = null
    ActiveObserver.instance.titleBorderImageSlice.value = '100%'
    ActiveObserver.instance.titleBorderImageWidth.value = '0'
    ActiveObserver.instance.titleBorderImageRepeat.value = 'stretch'
    ActiveObserver.instance.titleBorderStyle.value = "none"
}

function readImage(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
        if (!url) {
            reject('url为空')
            return
        }
        const image = new Image()
        image.crossOrigin = `Anonymous`
        image.src = url
        image.onload = () => {
            const imgWidth = image.width
            const imgHeight = image.height
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext("2d")
            canvas.width = imgWidth
            canvas.height = imgHeight
            ctx.drawImage(image, 0, 0)
            const data = canvas.toDataURL()
            resolve(data)
        }
        image.onerror = reject
    })
}

function readImageAndSlice(url): Promise<{ imageData: string, sliceTop: number, sliceRight: number, sliceBottom: number, sliceLeft: number }> {
    return new Promise((resolve, reject) => {
        if (!url) {
            reject('url为空')
            return
        }

        const image = new Image()
        image.crossOrigin = `Anonymous`
        image.src = url
        image.onload = () => {
            const imgWidth = image.width
            const imgHeight = image.height
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext("2d")
            canvas.width = imgWidth
            canvas.height = imgHeight
            ctx.drawImage(image, 0, 0)
            let topStart, topEnd, bottomStart, bottomEnd, leftStart, leftEnd, rightStart, rightEnd
            const imageData = ctx.getImageData(0, 0, imgWidth, imgHeight)
            console.log(`readImageAndSlice, load ${url} success, imgWidth:${imgWidth}, imgHeight:${imgHeight}, dataWidth:${imageData.width}, dataHeight:${imageData.height} imageDataSize:${imageData.data.length}`)
            // 先获取顶部第一行中的点9信息。i = 1, i < imgWidth - 1 是为了去除左右两边顶点的数据
            for (let i = 1; i < imgWidth - 1; i++) {
                const red = imageData.data[i * 4]
                const green = imageData.data[i * 4 + 1]
                const blue = imageData.data[i * 4 + 2]
                const alpha = imageData.data[i * 4 + 3]
                if (alpha == 255 && red == 0 && green == 0 && blue == 0) {
                    if (!topStart) {
                        topStart = i
                    }
                } else {
                    if (topStart) {
                        topEnd = i - 1
                        //一行中如果存在多段点9图的纯黑像素，这种点9图无法转换为js中的border image，因此不再继续解析。
                        break;
                    }
                }
            }
            if (topStart) {
                //图片最后一行的点9信息不单独读取像素值而是根据top来初始化bottom相关参数，这个是参考了master分支当前的写法
                bottomStart = topStart
                bottomEnd = imgWidth - 2 //-2目的是剔除左右边缘两个像素
            }

            //获取左侧第一列的点9信息
            for (let i = 1; i < imgHeight - 1; i++) {
                //读取图片第一列的点9信息
                const red = imageData.data[i * imgWidth * 4]
                const green = imageData.data[i * imgWidth * 4 + 1]
                const blue = imageData.data[i * imgWidth * 4 + 2]
                const alpha = imageData.data[i * imgWidth * 4 + 3]
                if (alpha == 255 && red == 0 && green == 0 && blue == 0) {
                    if (!leftStart) {
                        leftStart = i
                    }
                } else {
                    if (leftStart) {
                        leftEnd = i - 1
                        //一列中如果存在多段点9图的纯黑像素，这种点9图无法转换为js中的border image，因此不再继续解析。
                        break;
                    }
                }
            }
            if (leftStart) {
                rightStart = leftStart
                rightEnd = imgHeight - 2 //-2目的是剔除上下边缘两个像素
            }
            console.log(`readImageAndSlice, leftStart:${leftStart}, leftEnd:${leftEnd}, topStart:${topStart}, topEnd:${topEnd}`)

            const newCanvas = document.createElement('canvas')
            newCanvas.width = imgWidth - 2
            newCanvas.height = imgHeight - 2
            const newCtx = newCanvas.getContext('2d')
            // 实际使用的图像数据需要将外边缘一个像素宽度的点9信息剔除
            newCtx.putImageData(imageData, 0, 0, 1, 1, imgWidth - 2, imgHeight - 2)
            const dataBase64 = newCanvas.toDataURL()

            // sliceXXX 为剔除原点9图上下左右边缘像素后的距离参数，而leftXXX,rightXXX,topXXX,bottomXXX是index，需要注意中间的转换。
            let sliceTop = -1, sliceRight = -1, sliceBottom = -1, sliceLeft = -1
            if (leftStart && leftEnd && topStart && topEnd) {
                sliceTop = leftStart - 1 // 减去顶部点9信息所占用的一个像素，
                sliceRight = imgWidth - 2 - topEnd
                sliceBottom = imgHeight - 2 - leftEnd
                sliceLeft = topStart - 1 // 减去左侧点9信息所占用的一个像素，
            } else {
                Log.e("TipTap", `read slice fail! leftStart:${leftStart}, leftEnd:${leftEnd}, topStart${topStart}, topEnd:${topEnd}`)
            }
            resolve({ imageData: dataBase64, sliceTop: sliceTop, sliceRight: sliceRight, sliceBottom: sliceBottom, sliceLeft: sliceLeft })
        }
        image.onerror = reject
    })
}

export function getFocusedEditor(titleEditor: Editor, contentEditor: Editor): FocusedEditor {
    var editor: Editor | undefined = undefined
    var isTitleEditor = false
    var isContentEditor = false
    if (titleEditor.view.hasFocus()) {
        Log.d('Tiptap', `getFocusedEditor: title editor`, true)
        editor = titleEditor
        isTitleEditor = true
    } else if (contentEditor.view.hasFocus() || isAttachElementFocus()) {
        Log.d('Tiptap', `getFocusedEditor: content editor`, true)
        editor = contentEditor
        isContentEditor = true
    } else {
        Log.d('Tiptap', `getFocusedEditor: null`, true)
    }
    return {
        editor: editor,
        isTitleEditor: isTitleEditor,
        isContentEditor: isContentEditor,
    }
}

export function getEditorName(editor: Editor): string {
    var editorName = Constants.TIPTAP_CONTENT_CLASS_NAME
    const attributes = editor.options.editorProps.attributes
    if (attributes) {
        if (attributes['class'].includes(Constants.TIPTAP_TITLE_CLASS_NAME)) {
            editorName = Constants.TIPTAP_TITLE_CLASS_NAME
        } else if (attributes['class'].includes(Constants.TIPTAP_CONTENT_CLASS_NAME)) {
            editorName = Constants.TIPTAP_CONTENT_CLASS_NAME
        }
    }
    return editorName
}

export function captureElement(data: string, callback: (result: string) => void) {
    const elementInfos: Array<CaptureElementInfo> = JSON.parse(data)
    const elements = elementInfos.map(elementInfo => getHTMLElement(elementInfo))
    const resultData = []
    const promises = elements.map(element => validHtml2Canvas(element))
    Promise.allSettled(promises).then(results => {
        results.forEach((result, index) => {
            let base64Data = ""
            if (result.status === 'fulfilled') {
                base64Data = result.value
            } else {
                Log.i("TipTap", `captureElement, capture element fail for:${elementInfos[index].id}`)
            }
            resultData.push({ id: elementInfos[index].id, savePath: elementInfos[index].savePath, data: base64Data })
        })
        const resultStr = JSON.stringify(resultData)
        console.log(`captureElement result:${resultStr.length}`)
        callback(resultStr)
    }).catch((error) => {
        Log.i("TipTap", `captureElement error :${error}`)
        callback('')
    })
}

export function captureSingleElement(data: string, callback: (result: string) => void) {
    const elementInfo: CaptureElementInfo = JSON.parse(data)
    const element = getHTMLElement(elementInfo)
    if (element) {
        const resultData = []
        const options = {
            backgroundColor: "#00000000" // null或transparent可将canvas背景设置为透明
        }
        html2canvas(element, options).then((canvas) => {
            const base64Data = canvas.toDataURL("image/png")
            resultData.push({ id: elementInfo.id, savePath: elementInfo.savePath, data: base64Data })
            const resultStr = JSON.stringify(resultData)
            Log.i('TipTap', `captureSingleElement result:${resultStr.length}`)
            callback(resultStr)
        }).catch((error) => {
            Promise.reject(`${error} for ${element.tagName}`)
            callback('')
            Log.i("TipTap", `captureSingleElement, error for ${element.tagName}`)
        })
    }
}

function getHTMLElement(elementInfo: CaptureElementInfo): HTMLElement {
    const elementTagNameMap = new StringBuilder("")
    elementTagNameMap.append(elementInfo.tagName ? elementInfo.tagName : "")
    new Map(Object.entries(elementInfo.attrs)).forEach((value, key) => {
        elementTagNameMap.append('[').append(key).append('=').append('"').append(value).append('"').append(']')
    })
    const selectors = elementTagNameMap.toString()
    console.log(`getHTMLElement, selectors:${selectors}`)
    const elements = document.querySelectorAll(selectors)
    if (elements) {
        if (elements.length > 0) {
            return elements[0] as HTMLElement
        }
    }
    return null
}

function validHtml2Canvas(ele: HTMLElement) {
    if (ele) {
        const options = {
            backgroundColor: "#00000000" // null或transparent可将canvas背景设置为透明
        }
        return html2canvas(ele, options).then((canvas) => {
            return canvas.toDataURL("image/png")
        }).catch((error) => {
            return Promise.reject(`${error} for ${ele.tagName}`)
        })
    } else {
        return Promise.reject('ele 为 null')
    }
}

function getEditorByIndex(editorIndex: number, titleEditor: Editor, contentEditor: Editor): Editor | null {
    let editor: Editor
    if (editorIndex == -1) {
        const focusEditor = getFocusedEditor(titleEditor, contentEditor)
        editor = focusEditor.editor
        Log.d('TipTap', `getEditorByIndex, focusEditor:${focusEditor.isTitleEditor}-${focusEditor.isContentEditor}, ${titleEditor.isFocused}-${contentEditor.isFocused}`)
    } else if (editorIndex == 0) {
        editor = titleEditor
    } else {
        editor = contentEditor
    }
    if (!editor) {
        Log.d('TipTap', `getEditorByIndex, get default editor by null`)
        editor = contentEditor
    }
    return editor
}

/**
 * 获取编辑器中目标区域选中的文本
 * @param editorIndex  获取文本的目标区域。
 *               0：标题区域；
 *               1：内容区域;
 *              -1：和当前焦点所在区域一致，如果标题和正文区域都无焦点，则以正文区域为准
 * @returns 返回选中的字符串。未选中任何内容时将返回空字符串
 */
export function getSelectedText(editorIndex: number, titleEditor: Editor, contentEditor: Editor): string {
    const editor = getEditorByIndex(editorIndex, titleEditor, contentEditor)
    if (!editor.view.hasFocus()) {
        //无焦点时，清除原来的选中区域。避免在非编辑模式状态获取内容时返回非期望的文本
        const { from, to } = editor.state.selection
        Log.d('TipTap', `getSelectedText, reset selection:${from}-${to} by no focus`)
        editor.commands.focus(to)
    }
    const { from, to } = editor.state.selection
    return editor.state.doc.textBetween(from, to, "\n")
}

export function selectRangeText(from: number, to: number, editorIndex: number, titleEditor: Editor, contentEditor: Editor): string {
    const editor = getEditorByIndex(editorIndex, titleEditor, contentEditor)
    if (!editor.view.hasFocus()) {
        //无焦点时，将焦点定位到末尾
        if (DEBUG_ON_WEB) {
            editor.commands.focus()
        } else {
            editor.commands.focus('end')
        }
    }
    let textFrom = from
    let textTo = to
    if (from == to) {
        textFrom = 0
        textTo = editor.state.doc.content.size
    }
    return selectAndGetTrimmedText(editor, { from: textFrom, to: textTo }).text
}

/**
 * 获取编辑器中目标区域选中的文本
 * @param editorIndex  获取文本的目标区域。
 *               0：标题区域；
 *               1：内容区域;
 *              -1：和当前焦点所在区域一致，如果标题和正文区域都无焦点，则以正文区域为准
 * @param onlyText 只需要文本 附件信息不会被添加
 * @returns 返回选中的字符串。可能为空字符串
 *              目标区域无光标时，先选中所有文本并返回所有文本；(选中全文时会自动过滤首位的空行)
 *              目标区域有光标但是未选中文本时，先选中所有文本并选中的文本；(选中全文时会自动过滤首位的空行)
 *              目标区域有光标且有选中部分文本时，直接返回选中的文本；
 */
export function selectAndGetAllText(editorIndex: number, titleEditor: Editor, contentEditor: Editor, onlyText = true): AIGCSelectionValue {
    const editor = getEditorByIndex(editorIndex, titleEditor, contentEditor)
    if (!editor.view.hasFocus()) {
        //无焦点时，将焦点定位到末尾
        if (DEBUG_ON_WEB) {
            editor.commands.focus()
        } else {
            editor.commands.focus('end')
        }
    }
    const { empty, from, to } = editor.state.selection
    let isAttSelected = false
    if(editor.state.selection instanceof NodeSelection){
        isAttSelected = isAttachNode(editor.state.selection.node)
    }
    let textFrom = from
    let textTo = to
    if (empty || isAttSelected) {
        textFrom = 0
        textTo = editor.state.doc.content.size
    }
    return selectAndGetTrimmedText(editor, { from: textFrom, to: textTo }, onlyText)
}

/**
 * 选中并获取编辑器中目标区域光标前面的所有文本
 * @param editorIndex  获取文本的目标区域。
 *               0：标题区域；
 *               1：内容区域;
 *              -1：和当前焦点所在区域一致，如果标题和正文区域都无焦点，则以正文区域为准
 *
 * @param onlyText 只需要文本 附件信息不会被添加
 *
 * @returns 返回的字符串。可能为空字符串
 *              目标区域无光标时，先选中所有文本并返回所有文本；(选中全文时会自动过滤首位的空行)
 *              目标区域有光标但是未选中文本时，先选中目标区域内光标前的所有文本并返回选中的文本；(向前选中光标前所有文本时会自动过滤首位的空行)
 *              目标区域有光标且有选中文本时，直接返回已选中的文本
 */
export function selectAndGetForwardAllText(editorIndex: number, titleEditor: Editor, contentEditor: Editor, onlyText = true): AIGCSelectionValue {
    const editor = getEditorByIndex(editorIndex, titleEditor, contentEditor)
    if (!editor.view.hasFocus()) {
        //无焦点时，将焦点定位到末尾
        if (DEBUG_ON_WEB) {
            editor.commands.focus()
        } else {
            editor.commands.focus('end')
        }
    }
    const { empty, from, to } = editor.state.selection
    let textFrom = from
    let textTo = to
    if (empty) {
        textFrom = 0
    }
    return selectAndGetTrimmedText(editor, { from: textFrom, to: textTo }, onlyText)
}

/**
 * 将指定range范围内的文本过滤首位空行后选中并返回选中区域内的文本信息
 * @param editor
 * @param range 数据范围
 * @param onlyText 只需要文本 附件信息不会被添加
 * @returns
 */
export function selectAndGetTrimmedText(editor: Editor, range: Range, onlyText = true): AIGCSelectionValue {
    let from = range.from
    let to = range.to
    let validTextFrom, validTextTo
    editor.state.doc.nodesBetween(from, to, (node, pos) => {
        if (isAttachNode(node)) {
            //选区起始或结束点在附件范围内时，需要更新选区到附件后面或前面
            return false
        }
        if (node.isText) {
            if (!validTextFrom) {
                if (pos >= from) {
                    validTextFrom = pos
                } else if (pos + node.nodeSize >= from) {
                    validTextFrom = from
                }
            }
            if (pos + node.nodeSize <= to) {
                validTextTo = pos + node.nodeSize
            } else {
                validTextTo = to
            }
        }
    })
    if (validTextTo) {
        to = validTextTo
    }
    if (validTextFrom) {
        from = validTextFrom
    } else {
        // 如果无有效文字，选区起点和终点保持一致
        from = to
    }
    editor.commands.setTextSelection({ from: from, to: to })
    Log.d('TipTap', `selectAndGetTrimmedText, result:${from}-${to}, origin:${range.from}-${range.to}`, true)
    // 对象字符索引从1开始 eg: !(01)[对象]
    let attachIndex = 1
    const attachMap = {}
    //获取from-to位置的文本信息，并将附件节点转换为对象文本(eg: !(01)[对象]),并记录在 attachMap 中
    const text = getTextBetween2(editor.state, from, to, '\n', (attachNode) => {
        if (onlyText) {
            //节点是leaf
            return (attachNode.isLeaf ? '' : `\n`) + ``
        } else {
            // 如果只有一位数 需要在前面补0(01) 两位以上则不需要
            let stringIndex = String(attachIndex).padStart(2, '0')
            // 如果是附件 就push 一个指定的字符
            attachMap[stringIndex] = attachNode.toJSON()
            // 附件需要单独显示一行
            attachIndex++
            return (attachNode.isLeaf ? '' : `\n`) + `!(${stringIndex})[对象]`
        }
    })
    if (DEBUG_ON_WEB) {
        console.log(`selectAndGetTrimmedText, text:${text}`)
    }
    return { text, attachMap }
}

export function handleEnterAtTitleEditor(contentEditor: Editor) {
    contentEditor.commands.focus('start')
    const { $from } = contentEditor.state.selection
    const { parent } = contentEditor.state.doc.resolve($from.pos)
    console.log(`handleEnterAtTitleEditor: $from.pos=${$from.pos}, isBlock=${parent.isBlock}, isTextblock=${parent.isTextblock}`)
    if (parent && parent.isBlock && !parent.isTextblock) {
        Log.d(`Tiptap`, `handleEnterAtTitleEditor: insertEmptyLine`, true)
        contentEditor.commands.insertContentAt($from.pos, '<p></p>')
    }
}

export function increaseIndent(titleEditor: Editor, contentEditor: Editor) {
    const focusedEditor = getFocusedEditor(titleEditor, contentEditor).editor
    let result = false
    if (focusedEditor) {
        result = focusedEditor.chain().focus().increaseIndent().run()
    }
    return result
}

export function decreaseIndent(titleEditor: Editor, contentEditor: Editor) {
    const focusedEditor = getFocusedEditor(titleEditor, contentEditor).editor
    let result = false
    if (focusedEditor) {
        result = focusedEditor.chain().focus().decreaseIndent().run()
    }
    return result
}

export function updateElementAttributes(data: string): boolean {
    const updateElementAttrs: UpdateElementAttrs = JSON.parse(data)
    const elements = document.querySelectorAll(updateElementAttrs.elementSelectors)
    if (elements.length <= 0) {
        return false
    }
    elements.forEach((element) => {
        new Map(Object.entries(updateElementAttrs.newAttrs)).forEach((value, key) => {
            const htmlElement = (element as HTMLElement)
            if (key in htmlElement.style) {
                htmlElement.style.setProperty(key, value)
            } else {
                htmlElement.setAttribute(key, value)
            }
        })
    })
    return true
}

export function doInsertTable(contentEditor: Editor, attachId: string) {
    let result = false
    const inEditMode = isEditMode()
    result = contentEditor.chain().command(({ commands }) => {
        if (!inEditMode) {
            commands.focus('end')
        }
        return true
    }).setSelectionAfterTable().insertTable({ editMode: inEditMode, rows: 2, cols: 2, withHeaderRow: false, attachId: attachId }).run()
    return result
}

export function getNodeByAttachId(contentEditor: Editor, attachid: string) {
    let val = { position: 0, node: null, preNode: null, nextNode: null,index: 0 }
    if(!attachid) return val
    const { content } = contentEditor.state.doc.content as any
    let position = 0
    let node = null
    let preNode = null
    let nextNode = null
    let index = 0
    for(let i = 0; i < content.length; i++) {
      const item = content[i]
      if(item?.attrs?.attachid === attachid) {
        node = item
        preNode = i === 0 ? null : content[i - 1]
        nextNode = i === content.length - 1 ? null : content[i + 1]
        index = i
        position += item.nodeSize
        break
      }
      position += item.nodeSize
    }
    if(!node) return val
    return { node, position, preNode, nextNode, index }
  }

export function getNodeInfoByIndex(contentEditor: Editor, index: number){
    let val = { position: 0, node: null, preNode: null, nextNode: null,index: 0 }
    if(!index && index !== 0) return val
    const { content } = contentEditor.state.doc.content as any
    let position = 0
    let node = null
    let preNode = null
    let nextNode = null
    for(let i = 0; i <= index; i++) {
      const item = content[i]
      if(i === index) {
        node = item
        preNode = i === 0 ? null : content[i - 1]
        nextNode = i === content.length - 1 ? null : content[i + 1]
        position += item.nodeSize
        break
      }
      position += item.nodeSize
    }
    if(!node) return val
    return { node, position, preNode, nextNode, index }
}

/**
 * 当 将光标聚焦在文档最后的时候
 * 如果最后一个是附件, 就在末尾添加一个空行
*/
export function addEmptyLineIfLastIsAttach(contentEditor: Editor) {
    const { lastChild, size } = contentEditor.state.doc.content
    if(isAttachNode(lastChild)) {
        contentEditor.commands.insertContentAt(size, `<p></p>`)
    }
}

export function isTitleEditorView(view: EditorView): boolean {
    return view.dom.classList.contains(Constants.TIPTAP_TITLE_CLASS_NAME)
}

export function isContentEditorView(view: EditorView): boolean {
    return view.dom.classList.contains(Constants.TIPTAP_CONTENT_CLASS_NAME)
}

export function temporarilyDisableEditor(editor: Editor, recoverTime: number = 100) {
    editor.setEditable(false)
    setTimeout(() => {
        editor.setEditable(true)
    }, recoverTime);
}

export function isEditMode(): boolean {
    return ActiveObserver.instance.editorUiMode.value == '3'
}

/**
 * 返回页面上所有表格元素宽度最大的那一个(包含外层祖先元素们设置的padding)
 * 页面上没有表格或者表格宽度没有超出视图宽度 会返回视图宽度
 * 同时会将表格移动到最左侧  table.style.transform = '0'
*/
function getMaxClientTableWidth(): number {
    let L = 'get-max-client-table-width'
    const tableWrapper = document.getElementsByClassName('tableWrapper')
    if (tableWrapper.length === 0) return innerWidth
    const offsetWidths = [...tableWrapper].map(item => {
        const table = item.getElementsByTagName('table')[0]
        if (!table) return 0
        ;(table as HTMLTableElement ).style.transform = (table as HTMLTableElement ).style.transform.replaceAll(/\d+/g, '0')
        const { width } = table.getBoundingClientRect()
        // 获取表格左右两侧的距离页面两侧的距离
        const { left, right: _right } = item.getBoundingClientRect()
        const right = innerWidth - _right
        console.log(L, width, left, right, table.getAttribute('attachid'))
        return width + left + right
    })
    console.log('fix-width-when-image-share', Math.ceil(Math.max(...offsetWidths)))
    return Math.ceil(Math.max(...offsetWidths))
}
/**
 * 图片分享的时候,  需要将卡片的宽度固定在编辑状态下的宽度
 * 会操作包含fix-width-when-image-share属性的元素
*/
function fixWidthWhenImageShare() {
    let L = 'fix-width-when-image-share'
    // 设置包含fix-width-when-image-share属性的宽度
    for(const item of (document.querySelectorAll('[fix-width-when-image-share]') as NodeListOf<HTMLElement>)) {
        const width = item.style.width
        if(width) item.setAttribute('before-width', width)
        item.style.width = item.getBoundingClientRect().width + 'px'
    }
    const tableWrapper = document.getElementsByClassName('tableWrapper')
    if (tableWrapper.length === 0) return
    const style = document.head.appendChild(document.createElement('style'))
    style.classList.add(Constants.FIX_WIDTH_WHEN_IMAGE_SHARE_STYLE_CLASS)
    ;[...tableWrapper].forEach(item => {
        const table = item.getElementsByTagName('table')[0]
        if(!table) return
        const tableSelector = `#app .tiptapContent table[attachid="${table.getAttribute('attachid')}"]`
        // 表格设置了行内样式 width 和 min-width 需要增加权重
        const { width } = table.getBoundingClientRect()
        style.innerHTML += `
            ${tableSelector}{
                width: ${width}px !important;
                min-width: ${width}px !important;
                max-width: ${width}px !important;
            }
        `
        const trs = `#app .tiptapContent table[attachid="${table.getAttribute('attachid')}"] tr `
        const trChildren = document.querySelector(trs).children
        ;[...trChildren].forEach((item, index) => {
            const { width } = item.getBoundingClientRect()
            style.innerHTML += `
                ${trs}>td:nth-of-type(${index+1}), ${trs}>td:nth-of-type(${index+1}) p{
                    min-width: ${width}px;
                    max-width: ${width}px;
                    width: ${width}px;
                }
                ${tableSelector} > colgroup > col:nth-of-type(${index+1}){
                    width: ${width}px !important;
                }

            `
        })
        console.log(L, document.querySelector(trs))
    })

    console.log(L, style)
}
/**
 * 图片分享操作结束
 * 将设置的宽度恢复到之前的值
*/
function restoreWidthImageShareFinish(){
    for(const item of (document.querySelectorAll('[fix-width-when-image-share]') as NodeListOf<HTMLElement>)) {
        const width = item.getAttribute('before-width')
        width ? (item.style.width = width) : item.style.removeProperty('width')
    }
    // 移除样式分享图片时新增加的图片
    const styles = document.getElementsByClassName(Constants.FIX_WIDTH_WHEN_IMAGE_SHARE_STYLE_CLASS)
    for(const item of styles) {
        item.remove()
    }
}

// 将表格的选中的单元格生成图片并返回
async function generateImageFromSelectedTable(contentEditor: Editor):Promise<object> {
    const empty = {data:'', width: 0}
    const L = `generate-image-from-selected-table`
    let domAtPos = contentEditor.view.domAtPos(contentEditor.state.selection.to).node as HTMLElement
    if(!domAtPos) {
        console.log(L, '通过选区位置获取dom节点失败, selection.to: ', contentEditor.state.selection.to)
        return empty
    }
    let tableWrapper= null
    while(true) {
        if(domAtPos?.classList?.contains('tableWrapper')){
            tableWrapper = domAtPos
            break
        }
        if(domAtPos === document.body) {
            console.log(L, '没有找到表格容器元素: ', contentEditor.state.selection.to)
            return empty
        }
        domAtPos = domAtPos.parentElement
    }
    const _wrapperParent = tableWrapper.parentElement.cloneNode(false) as HTMLElement
    const _tableWrapper = tableWrapper.cloneNode(true) as HTMLElement
    const table = tableWrapper.getElementsByTagName('table')[0]
    const _table = _tableWrapper.getElementsByTagName('table')[0]
    const trChildren = table.getElementsByTagName('tr')[0].children
    const trChildLength = trChildren.length

    tableWrapper.parentElement.parentElement.appendChild(_wrapperParent)
    _table.style.width = 'auto'
    _table.style.minWidth = 'auto'
    _table.style.transform = 'none'
    _wrapperParent.appendChild(_tableWrapper)
    _wrapperParent.classList.add('generate-table-image-clone-node')
    const _tds = [..._wrapperParent.getElementsByTagName('td')]

    // 固定每个单元格的宽度 删除没有被选中的单元格
    for (let i = 0; i < _tds.length; i++) {
        const item = _tds[i]
        if (!item.classList.contains('selectedCell')) {
        item.remove()
        continue
        }
        if (i / trChildLength < 1) item.style.width = trChildren[i % trChildLength].getBoundingClientRect().width + 'px'

        // 可能有多个p节点
        for(const p of item.children) {
            // p节点可能有多个子节点比如 Text + <a>
            for(const _pChild of p.children) {
                let baseElement = _pChild.firstElementChild as HTMLElement
                if(baseElement) {
                    while(baseElement.firstElementChild) {
                        baseElement = baseElement.firstElementChild as HTMLElement
                    }
                    // 删除线和下划线在使用html2canvas绘制的时候, 如果不是最后的子节点, 该样式会被过滤掉, 显式的设置行内样式来规避这个问题
                    if(_pChild.getElementsByTagName('s').length || _pChild.getElementsByTagName('del').length || _pChild.tagName.toLowerCase() === 's') {
                        baseElement.style.textDecorationLine += ' line-through '
                    }

                    if(_pChild.getElementsByTagName('u').length || _pChild.tagName.toLowerCase() === 'u') {
                        baseElement.style.textDecorationLine += ' underline '
                    }
                    console.log(L, 'item', baseElement)
                }
            }
        }

    }
    // 删除添加的修饰器
    for (const item of [..._wrapperParent.getElementsByTagName('div')]) {
        ;(item.className.includes('ProseMirror-widget') || item.className.includes('bscroll-horizontal-scrollbar')) && item.remove()
    }
    const trs = _wrapperParent.getElementsByTagName('tr')
    const cols = _wrapperParent.getElementsByTagName('col')
    // 删除空行
    for (const item of [...trs, ...cols]) item.childElementCount === 0 && item.remove()

    if(trs.length === 0) {
        console.log(L, '当前表格中未选中任何单元格: ', contentEditor.state.selection.from, contentEditor.state.selection.to)
        return empty
    }
    const { width, height } = _wrapperParent.getBoundingClientRect()
    _wrapperParent.style.position = 'fixed'
    _wrapperParent.style.left = -width + -innerWidth + 'px'

    const canvas = await html2canvas(_wrapperParent, { width: width + 5, height, backgroundColor: null })
    setTimeout(() => _wrapperParent.remove(), 0);
    if(!canvas.toDataURL) return empty
    const types = Constants.GENERATE_TABLE_IMAGE_TYPE
    let result = ''
    let index = 0
    while(!result && index < types.length) {
        let type = types[index]
        result = canvas.toDataURL(type, 1)
        // 判断生成的图片是否异常
        if(result.startsWith(`data:${type};base64,`)) break
        result = ''
        index++
    }
    if(!result) return empty
    return new Promise((resolve, reject)=> {
        const image = new Image()
        image.src = result
        // 图片加载完成获取图片尺寸
        image.onload = function() {
            console.log(L, 'image', result)
            resolve({ data: result.split(',')[1], width: image.naturalWidth })
        }
        image.onerror = function() {
            reject(empty)
        }
    })
}


/**
 * 获取可用的光标位置
 * dir > 0 则向右寻找  小于0 则向左寻找
 * 如果按照指定方向没有找到可用的光标位置, 则会反向寻找,
 * 如果整个文档都没有找到, 就返回原始pos
*/
export function getSafeCursorPosition(state: EditorState, pos, dir = 1) {
    // debugger
    let _pos = (pos < 0 || pos > state.doc.content.size) ? pos < 0 ? 0 : state.doc.content.size : pos
    let isReverse = false
    let node = dir > 0 ? 'nodeAfter' : 'nodeBefore'
    while(true) {
      if(state.doc.resolve(_pos)[node]?.type?.name === 'text') break
      dir > 0 ? _pos++ : _pos--
      if(_pos < 0 || _pos > state.doc.content.size) {
        if(isReverse) {
            console.log(`pos: ${pos} 文档内没有可用的光标位`)
            return pos
        }
        isReverse = true
        dir = dir > 0 ? -1 : 1
        _pos = pos
        node = dir > 0 ? 'nodeAfter' : 'nodeBefore'
      }
    }
    return _pos
}

// 设置使用ai功能时, 文本的选中色
function setAigcSelecitonColor() {
    ActiveObserver.instance.otherScenesTextColorHighlight.value = (ActiveObserver.instance.aigcTextUiModeApplyCss.value == 0) ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255,255,255,0.12)'
}
