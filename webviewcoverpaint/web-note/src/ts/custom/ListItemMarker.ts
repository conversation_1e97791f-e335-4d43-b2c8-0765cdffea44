import { Extension} from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import * as ActiveObserver from '@ts/TipTapAttrsObserver'
import {isBulletHXListNode, isBulletListNode, isHeadingNode, isMultiHeadingNode, isOrderedListNode, isParagrahNode, isTaskListNode} from "@ts/custom/helpers";
import { isLargeOrderedList } from './ordered-list/ordered-list'

export const ListItemMarker = Extension.create({
    name: 'list-item-marker',
    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('CustomListItemStyle'),
                appendTransaction: (transactions, oldState, newState) => {
                    const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)
                    if (!docChanges) {
                        return
                    }
                    const state = newState
                    const { tr } = state
                    const doc = state.doc
                    const storedMarks = tr.storedMarks
                    doc.descendants((node, pos) => {
                        const isBulletList = isBulletListNode(node)
                        const isBulletListHX = isBulletHXListNode(node)
                        const isOrderedList = isOrderedListNode(node)
                        const isTaskList = isTaskListNode(node)
                        if (!isBulletList && !isBulletListHX && !isOrderedList && !isTaskList) {
                            return false
                        }
                        const largeOrderedList = isOrderedList && isLargeOrderedList(node.attrs['start'])
                        let marker;
                        if (isOrderedList) {
                            marker = node.attrs['start'] - 1
                        } else {
                            marker = isOrderedList ? 0 : (isBulletList ? '\u2022' : '\u2013')
                        }
                        node.descendants((liNode, liNodePos) => {
                            let hSpan = false
                            let liFontSize
                            let isHollowCircle = false
                            let isLozenge = false
                            if (isBulletList) {
                                const textIndent = liNode.attrs.textIndent ? liNode.attrs.textIndent : 0
                                if (textIndent > 0) {
                                    if (textIndent == 1) {
                                        marker = '\u25e6' //空心圆
                                        isHollowCircle = true
                                    } else {
                                        marker = '\u2b29' //菱形
                                        isLozenge = true
                                    }
                                } else {
                                    marker = '\u2022' //实心圆
                                }
                            }

                            liNode.descendants((pNode, pNodePos) => {
                                if (isOrderedList) {
                                    marker = +marker + 1
                                }
                                if (isMultiHeadingNode(node)) {
                                    hSpan = true
                                    if (pNode.attrs.multiHeading === "1") {
                                        liFontSize = `${ActiveObserver.instance.h1TitleSize.value}rem`
                                    } else if (pNode.attrs.multiHeading === "2") {
                                        liFontSize = `${ActiveObserver.instance.h2TitleSize.value}rem`
                                    } else {
                                        liFontSize = `${ActiveObserver.instance.h3TitleSize.value}rem`
                                    }
                                } else {
                                    if (isTaskList) {
                                        // 代办列表，li的字号取该列表项中的最大字号，方便代办符号居中对齐
                                        let maxFontSize = 0
                                        pNode.descendants(textNode => {
                                            const textStyleMark = textNode.marks.find(markItem => markItem.type?.name === state.schema.marks.textStyle.name)
                                            let fontSize = parseFloat(textStyleMark?.attrs?.fontSize)
                                            fontSize = isNaN(fontSize) ? 1 : fontSize
                                            if (fontSize > maxFontSize) {
                                                maxFontSize = fontSize
                                            }
                                        })
                                        if (maxFontSize > 0) {
                                            liFontSize = `${maxFontSize}rem`
                                        }
                                    } else {
                                        if (pNode.firstChild) {
                                            const textStyleMark = pNode.firstChild.marks.find(markItem => markItem.type?.name === state.schema.marks.textStyle.name)
                                            const fontSize = textStyleMark?.attrs?.fontSize
                                            if (!liFontSize && fontSize) {
                                                liFontSize = fontSize
                                                console.log(`first text node font size:${fontSize}`)
                                            }
                                        }
                                    }
                                    if (!liFontSize) {
                                        //空段落，或者第一个textNode无fontSize属性，都取默认值
                                        liFontSize = `${ActiveObserver.instance.normalPTitleSize.value}rem`
                                    }
                                }
                                return false
                            })

                            if (liNode.attrs['itemFontSize'] != liFontSize) {
                                tr.setNodeAttribute(pos + liNodePos + 1, 'itemFontSize', liFontSize)
                            }
                            if (largeOrderedList && liNode.attrs['itemMarker'] != marker) {
                                tr.setNodeAttribute(pos + liNodePos + 1, 'itemMarker', marker)
                            }
                            return false
                        })
                    })
                    storedMarks && tr.ensureMarks(storedMarks)
                    return tr
                }
            })
        ]
    },
})
