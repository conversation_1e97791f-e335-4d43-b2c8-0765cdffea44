import { EditorState } from '@tiptap/pm/state'

export const hasListAtFromOrBefore = (editorState: EditorState, name: string, parentListTypes: string[]) => {
  const { $from, empty } = editorState.selection

  let previousNode
  if (empty) {
    //empty为true，判断前一个节点是否为列表节点
    const previousNodePos = Math.max(0, $from.pos - 2) //-2是为了定位到前面的orderlist等列表标签内部
    previousNode = editorState.doc.resolve(previousNodePos).node()
  } else {
    if ($from.depth == 0) {
      // depth为0时，可能选中的是图片等block节点，-1即可定位到前面的列表标签内部
      previousNode = editorState.doc.resolve(Math.max(0, $from.pos - 1)).node()
    } else {
      previousNode = $from.node(1)
    }
  }

  if (!previousNode || !parentListTypes.includes(previousNode.type.name)) {
    return false
  }
  return true
}
