import { RawCommands } from "@tiptap/vue-3";
import { parseAigcHtmlContent } from "./aigcHelper"
import { isListType } from "../../helpers";
import * as Log from '@ts/utils/Logger'
import { Fragment } from "@tiptap/pm/model";
import { liftTarget } from "@tiptap/pm/transform";
import { getSafeCursorPosition } from '@ts/EditorUtils'

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        insertAigcHtmlContent: {
            /**
             * 在指定位置[from, to]插入便签 aigc 返回的 html 内容。(非标准 html 内容，当前仅支持极少数html标签)
             * @param from 插入的起始位置
             * @param to 插入的结束位置
             * @param htmlContent 需要插入的html 内容
             * @param isContinueWrite 是否为续写操作。为true时，第一个段落的文字内容会与光标位置合并到一个段落显示；false 则会新起一个段落插入
             */
            insertAigcHtmlContent: (from: number, to: number, htmlContent: string, isContinueWrite: boolean) => ReturnType;
        }
    }
}

export const insertAigcHtmlContent: RawCommands['insertAigcHtmlContent'] = (from, to, htmlContent, isContinueWrite) => ({ editor, chain }) => {
    console.log(`insertAigcHtmlContent, from:${from}, to:${to}, htmlContent:${htmlContent}, isContinueWrite:${isContinueWrite}`)
    const fragment = parseAigcHtmlContent(editor, htmlContent, isContinueWrite)
    if (fragment.size <= 0) {
        Log.e("TipTap", `insertAigcHtmlContent, content is empty!`, true)
        return false
    }
    const splitNode = splitTextNode(fragment)
    const textNode = splitNode.textNode
    const blockNode = splitNode.blockNode
    let offset = 0
    return chain().command(({ commands }) => {
        if (to - from <= 0) {
            return true
        }
        return commands.deleteRange({ from: from, to: to })
    }).command(({ state, tr }) => {
        if (textNode.size > 0) {
            // 插入数据的第一个节点是文本节点，则直接先将文本节点插入到光标处，剩余其他节点需要插入到下一行
            const newFrom = tr.mapping.map(from)
            const $newFromPos = state.doc.resolve(newFrom)
            if ($newFromPos.depth == 0) {
                //当前插入位置不是段落节点中，需要给firstTextNode额外包装一个p节点
                const newParagraphNode = state.schema.node(state.schema.nodes.paragraph, null, textNode)
                tr.insert(from, newParagraphNode)
            } else {
                tr.insert(from, textNode)
            }
        }
        return true
    }).command(({ state, tr }) => {
        if (blockNode.size > 0) {
            // 剩余节点数据大于0才继续处理
            const newFrom = tr.mapping.map(from)
            const $newFromPos = state.doc.resolve(newFrom)
            tr.split(newFrom, $newFromPos.depth)
        }
        return true
    }).command(({ state, commands, tr }) => {
        if (blockNode.size > 0) {
            // 剩余节点数据大于0才继续处理
            const newFrom = tr.mapping.map(from)
            const $newFromPos = state.doc.resolve(newFrom)
            const nodeTypeName = $newFromPos.node(1)?.type?.name
            if (isListType(state, nodeTypeName) && $newFromPos.node($newFromPos.depth).textContent.length == 0) {
                // split 后光标会更新到下一行，如果下一行的内容为空，说明插入的位置在列表项末尾。根据产品需求，先移除该行的列表属性，后面再删除该行
                const nodeRange = $newFromPos.blockRange($newFromPos)
                const target = nodeRange && liftTarget(nodeRange)
                tr.lift(nodeRange, target)
            }
        }
        return true
    }).command(({ state, tr }) => {
        if (blockNode.size > 0) {
            // 剩余节点数据大于0才继续处理
            const newFrom = tr.mapping.map(from)
            const $newFromPos = state.doc.resolve(newFrom)
            if ($newFromPos.node($newFromPos.depth).textContent.length == 0) {
                // 使用 replace 方式删除列表项末尾的空行
                tr.replaceWith($newFromPos.before(), $newFromPos.end(), blockNode)
            } else {
                const nodeTypeName = $newFromPos.node(1)?.type?.name
                if (isListType(state, nodeTypeName)) {
                    offset = $newFromPos.before(1) - newFrom
                    tr.insert($newFromPos.before(1), blockNode)
                } else {
                    offset = -$newFromPos.depth
                    tr.insert(newFrom - $newFromPos.depth, blockNode)
                }
            }
        }
        return true
    }).command(({ commands, tr, state }) => {
        const doc = state.doc
        // 需要将保存的from 和 to 位置寻找可用的光标位置 from 向有寻找  to向左寻找 getSafeCursorPosition
        const _from = getSafeCursorPosition(state, Math.max(0, from + 1), 1)
        const _to = getSafeCursorPosition(state, Math.min(doc.content.size, tr.mapping.map(from) + offset), -1)
        const range = { from: Math.max(0, _from), to: Math.min(doc.content.size, _to) }
        console.log('aigc-log-insert-selection', JSON.stringify(range))
        return commands.setTextSelection(range)
    }).run()
}

function splitTextNode(fragment: Fragment) {
    let foundTextNode = true
    let textNodeEnd = 0
    fragment.forEach((node, offset) => {
        if (foundTextNode) {
            if (node.isText) {
                textNodeEnd = offset + node.nodeSize
            } else {
                foundTextNode = false
            }
        }
    })
    return { textNode: fragment.cut(0, textNodeEnd), blockNode: fragment.cut(textNodeEnd, fragment.size) }
}