/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ByWebViewClient.kt
 * Description    : ByWebViewClient.kt
 * Version        : 1.0
 * Date           : 2023/6/5
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version>       <desc>
 * Chandler     2023/6/5         1.0           create
</desc></version></date></author> */
package com.oplus.notes.webviewcoverpaint.container.web

import android.content.Context
import android.os.Build
import com.heytap.tbl.webkit.WebResourceRequest
import com.heytap.tbl.webkit.WebResourceResponse
import com.heytap.tbl.webkit.WebView
import com.heytap.tbl.webkit.WebViewClient
import androidx.annotation.RequiresApi
import androidx.webkit.WebViewAssetLoader
import androidx.webkit.WebViewAssetLoader.ResourcesPathHandler
import java.io.File

class ByWebViewClient(val context: Context) : WebViewClient() {
    companion object {
        private const val TAG = "ByWebViewClient"
    }
    private val assetLoader = WebViewAssetLoader.Builder()
        // Web端字体指向系统/system/fonts/目录下的字体
        .addPathHandler("/assets/tiptap/assets/fonts/", SystemFontHandler())
        // 添加 tiptapcoverpaint 路径的字体处理器
        .addPathHandler("/assets/tiptapcoverpaint/assets/fonts/", SystemFontHandler())
        .addPathHandler("/assets/", WebViewAssetLoader.AssetsPathHandler(context))
        .addPathHandler("/res/", ResourcesPathHandler(context))
        .addPathHandler(AndroidResourcePathHandler.HANDLE_PATH_PREFIX, AndroidResourcePathHandler(context))
        .addPathHandler("/image_manager_disk_cache/", WebViewAssetLoader.InternalStoragePathHandler(
            context, getGlideCacheDir(context)
        ))
        .addPathHandler("/", WebViewAssetLoader.InternalStoragePathHandler(context, context.filesDir))
        .build()

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    override fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest): WebResourceResponse? {
        val response =  assetLoader.shouldInterceptRequest(request.url)
        return response?.toTBLWebResourceResponse()
    }
}

fun android.webkit.WebResourceResponse.toTBLWebResourceResponse(): WebResourceResponse {
    return WebResourceResponse(
        this.mimeType,
        this.encoding,
        this.data
    )
}

fun getDocThumbnailCacheDir(context: Context): File {
    val dir = File(context.cacheDir, "/doc_thumbnails/")
    if (!dir.exists()) {
        dir.mkdirs()
    }
    return dir
}

fun getGlideCacheDir(context: Context): File {
    val dir = File(context.cacheDir, "/image_manager_disk_cache/")
    if (!dir.exists()) {
        dir.mkdirs()
    }
    return dir
}