<template>
  <node-view-wrapper contenteditable="false" :attachid="node.attrs.attachid" v-attach-cursor="node.attrs.attachid">
    <div class="contactCard" :class="{ selected: props.node.attrs.selected }" :draggable="!isInterceptUserInteraction()"
      :type="props.node.attrs.htmlElementType" :attachid="props.node.attrs.attachid">
      <div class="contact-layout">
        <div class="contact-header">
          <div class="contact-info">
            <div class="contact-image">
              <img :src="'./icons/contact-card-icon.svg'" draggable="false" class="no-border" />
            </div>
            <span class="contact-card-name">{{ props.node.attrs.cardname }}</span>
          </div>
          <button class="contact-copy" @click="handleCopyContactClick(props.node.attrs.attachid)">
            {{ props.node.attrs.copy }}
          </button>
        </div>
        <div class="contact-details">
          <div class="contact-name-phone">{{ getDisplayName() }}</div>
          <div v-if="props.node.attrs.position && props.node.attrs.position.length > 0" class="contact-position">{{
            props.node.attrs.position }}</div>
          <div v-if="props.node.attrs.company && props.node.attrs.company.length > 0" class="contact-company">{{
            props.node.attrs.company }}</div>
        </div>
        <div class="contact-actions">
          <button class="contact_call" type="cardbutton" @click="handleContactCallClick(props.node.attrs.phone)"
            @touchstart="handleTouchStart($event)" @touchend="handleTouchEnd($event)"
            @touchcancel="handleTouchEnd($event)">
            {{ props.node.attrs.call }}
          </button>
          <button class="contact_save" type="cardbutton"
            @click="handleContactSaveClick(props.node.attrs.phone, props.node.attrs.name, props.node.attrs.company, props.node.attrs.position)"
            @touchstart="handleTouchStart($event)" @touchend="handleTouchEnd($event)"
            @touchcancel="handleTouchEnd($event)">
            {{ props.node.attrs.save }}
          </button>
        </div>
        <div v-if="props.node.attrs.selected" class="contact-layout-foreground"></div>
      </div>
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import * as ActiveObserver from "@ts/TipTapAttrsObserver"
import { NodeViewWrapper } from '@tiptap/vue-3'
import { ref } from "vue"
import { DEBUG_ON_WEB } from "@ts/EditorUtils"

const props = defineProps({
    node: {
        type: Object,
        required: true,
    }
})

function getDisplayName(): string {
  let displayName = ''
  if (props.node.attrs.name && props.node.attrs.phone) {
    displayName = props.node.attrs.name + " " + props.node.attrs.phone
  } else if (props.node.attrs.name) {
    displayName = props.node.attrs.name
  } else if (props.node.attrs.phone) {
    displayName = props.node.attrs.phone
  }
  return displayName
}

function handleCopyContactClick(attrId: string) {
  console.log(`handleCopyContactClick:${attrId}`)
  if (!DEBUG_ON_WEB) {
    (window as any).injectedObject?.onCopyViewClick(attrId, 'contact')
  }
}

function handleContactCallClick(phone: string) {
  console.log(`handleContactCallClick:${phone}`)
  if (!DEBUG_ON_WEB) {
    (window as any).injectedObject?.onContactCallClick(phone.toString())
  }
}

function handleContactSaveClick(phone: string, name: string, company: string, position: string) {
  console.log(`handleContactSaveClick:${phone}`)
  if (!DEBUG_ON_WEB) {
    (window as any).injectedObject?.onContactSaveClick(phone.toString(), name, company, position)
  }
}

function handleTouchStart(event) {
  event.target.classList.add('active')
}

function handleTouchEnd(event) {
  event.target.classList.remove('active')
}

function isInterceptUserInteraction(): boolean {
  return ActiveObserver.instance.isInterceptEditorClick.value === 'true'
}

const backgroundColorUnChecked = ActiveObserver.instance.backgroundColorUnChecked
const primaryTextColor = ActiveObserver.instance.primaryTextColor
const secondaryTextColor = ActiveObserver.instance.secondaryTextColor
const contactCopyTextColor = ActiveObserver.instance.contactCopyTextColor
const contactButtonBackColor = ActiveObserver.instance.contactButtonBackColor
const contactButtonPressBackColor = ActiveObserver.instance.contactButtonPressBackColor
const contactButtonTextColor = ActiveObserver.instance.contactButtonTextColor
const contactBorderRadius = ref("0.75rem")

</script>

<style lang="scss">
@import '../assets/styles/direction';
@import '../assets/styles/margin.scss';

// @include attachment-margin("contactCard");

.contactCard {
  width: 100%;
  max-width: 19.5rem;
}

.contact-layout {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: v-bind(contactBorderRadius);
  background-color: v-bind(backgroundColorUnChecked);
  padding-bottom: 1rem;
}

.contact-layout-foreground {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: var(--block-selected-color);
  border-radius: v-bind(contactBorderRadius);
}


.contact-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.contact-info {
  display: inline-flex;
  float: left;
  max-width: calc(100% - 3.5rem);
}

.contact-image {
  width: 1.125rem;
  height: 1.125rem;
  min-width: 1.125rem;
  margin-top: 0.875rem;
  @include margin-start(0.875rem);
  @include margin-end(0);
}

.contact-card-name {
  margin-top: 0.8rem;
  @include margin-start(0.375rem);
  @include margin-end(0.5rem);
  font-size: 0.75rem;
  color: v-bind(primaryTextColor);
  max-lines: 1;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
 }

.contact-copy {
  margin-top: 0.8rem;
  @include margin-start(0);
  @include margin-end(1.25rem);
  font-size: 0.75rem;
  color: v-bind(contactCopyTextColor);
  font-weight: 500;
  background-color: #00000000;
  border: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
}

.contact-name-phone {
  margin-top: 0.6875rem;
  @include margin-start(1rem);
  @include margin-end(1.75rem);
  font-size: 1rem;
  color: v-bind(primaryTextColor);
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
}

.contact-position {
  margin-top: 0.125rem;
  @include margin-start(1rem);
  @include margin-end(1.5rem);
  font-size: 0.75rem;
  color: v-bind(secondaryTextColor);
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
}

.contact-company {
  margin-top: 0.0625rem;
  @include margin-start(1rem);
  @include margin-end(1.5rem);
  font-size: 0.75rem;
  color: v-bind(secondaryTextColor);
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
}

.contact-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
}

.contact_call,
.contact_save {
  flex: 1;
  margin: 0 0.25rem;
  height: 2.25rem;
  background-color: v-bind(contactButtonBackColor);
  color: v-bind(contactButtonTextColor);
  font-size: 0.75rem;
  font-weight: 500;
  border: none;
  border-radius: 1.125rem; // radius是高度的一半，修改高度，radius要一起更新
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  user-select: none;
}

.contact_call {
  @include margin-start(1rem);
  @include margin-end(0.25rem);
  &.active {
    background-color: v-bind(contactButtonPressBackColor);
  }
}

.contact_save {
  @include margin-start(0.25rem);
  @include margin-end(1rem);
  &.active {
    background-color: v-bind(contactButtonPressBackColor);
  }
}

</style>
