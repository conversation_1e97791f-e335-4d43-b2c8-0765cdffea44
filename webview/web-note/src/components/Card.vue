<template>
    <node-view-wrapper contenteditable="false" :attachid="props.node.attrs.attachid" v-attach-cursor="node.attrs.attachid">
        <div class="card" fix-width-when-image-share :attachid="props.node.attrs.attachid" :class="{ selected: props.node.attrs.selected }"
            draggable="true" :type="props.node.attrs.htmlElementType" v-if="ActiveObserver.instance.shouShowCard.value">
            <div style="display: flex; flex-direction: row;   justify-content: space-between; align-items: center;">
            <div class="card-left">
                <!-- 条件渲染 title 和 content -->
                <!-- title 和 content 都不为空 -->
                <div v-if="props.node.attrs.title && props.node.attrs.content" class="card-title">
                    {{ props.node.attrs.title }}
                </div>
                <div v-if="props.node.attrs.title && props.node.attrs.content" class="pure-content">
                    {{ props.node.attrs.content }}
                </div>

                <!-- title 为空，content 不为空 -->
                <div v-else-if="props.node.attrs.content" class="pure-content-two">
                    {{ props.node.attrs.content }}
                </div>

                <!-- title 不为空，content 为空 -->
                <div v-if="props.node.attrs.title && !props.node.attrs.content" class="card-title">
                    {{ props.node.attrs.title }}
                </div>
                <div v-if="props.node.attrs.title && !props.node.attrs.content" class="pure-content">
                    {{ props.node.attrs.url }}
                </div>

                <!-- title 和 content 都为空 -->
                <div v-else-if="!props.node.attrs.title && !props.node.attrs.content" class="pure-content-two">
                    {{ props.node.attrs.url }}
                </div>
            </div>
            <div class="card-right">
                <div class="card-image">
                    <img id="card-image-img" :src="props.node.attrs.image" @error="handleImageError"
                        draggable="false" />
                </div>
            </div>
            <div v-if="props.node.attrs.selected" class="card-foreground"></div>
        </div>
        <div class="card-source" v-if="props.node.attrs.sourceLabel">{{ props.node.attrs.sourceLabel }}</div>
    </div>
    </node-view-wrapper>
</template>

<script setup lang="ts">
import * as ActiveObserver from "@ts/TipTapAttrsObserver"
import { NodeViewWrapper } from '@tiptap/vue-3'
import { ref } from "vue"

const props = defineProps({
    node: {
        type: Object,
        required: true,
    }
})

const handleImageError = () => {
  const svgContainer = document.getElementById('card-image-img') as HTMLImageElement
  svgContainer.src = `./icons/ic-link-default.svg`
}

const textColor = ActiveObserver.instance.pColor
const cardBorderColor = ActiveObserver.instance.cardBorderColor
const cardBackGroundColor = ActiveObserver.instance.cardBackGroundColor
const cardBorderRadius = ref("0.625rem")
const secondTextColor = ActiveObserver.instance.secondaryTextColor

</script>

<style lang="scss" scoped>
@import '../assets/styles/direction';
@import '../assets/styles/margin.scss';

// @include attachment-margin("card");
.card {
    width: 100%;
    height: fit-content !important;
    // border: 0.33px solid v-bind(cardBorderColor);
    border-radius: v-bind(cardBorderRadius);
    background: v-bind(cardBackGroundColor);
    padding: 0.75rem 0.9375rem;
    display: flex;
    flex-direction: column;
    position: relative;
}

.card-foreground {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: v-bind(cardBorderRadius);
    background: var(--block-selected-color);
}

.card-left {
    flex-grow: 1;
    /* 设置元素可以占据剩余的空间 */
    width: 80%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    @include margin-start(0);
    @include margin-end(12px);
}

.card-right {
    width: 40px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.card-title {
    font-size: 1rem;
    color: v-bind(textColor);
    margin-bottom: 0.1875rem;
    font-weight: 500;
    /* 禁止换行 */
    white-space: nowrap;
    /* 超出部分裁剪 */
    text-overflow: ellipsis;
    /* 必须有宽度才能起作用 */
    width: 100%;
    /* 强制一行内显示 */
    overflow: hidden;
    user-select: none;
}

.card-url {
    font-size: 0.875rem;
    color: v-bind(textColor);
    font-weight: 400;
    /* 禁止换行 */
    white-space: nowrap;
    /* 超出部分裁剪 */
    text-overflow: ellipsis;
    /* 必须有宽度才能起作用 */
    width: 100%;
    /* 强制一行内显示 */
    overflow: hidden;
    user-select: none;
}
.card-source{
    margin-top: 0.5rem;
    display: flex;
    height: 1.5rem;
    width: 100%;
    color: v-bind(secondTextColor);
    font-weight: 400;
    user-select: none;
    font-size: 0.75rem;
    border-top: 0.5px solid rgba(0, 0, 0, 0.12);
    align-items: flex-end;
    justify-content: flex-start;
}

.pure-content {
    font-size: 0.875rem;
    color: v-bind(secondTextColor);
    font-weight: 400;
    /* 禁止换行 */
    white-space: nowrap;
    /* 超出部分裁剪 */
    text-overflow: ellipsis;
    /* 必须有宽度才能起作用 */
    width: 100%;
    /* 强制一行内显示 */
    overflow: hidden;
    user-select: none;
}

.pure-content-two {
    font-size: 1rem;
    color: v-bind(textColor);
    font-weight: 500;
    /* 允许换行 */
    white-space: normal;
    /* 超出部分裁剪 */
    text-overflow: ellipsis;
    /* 必须有宽度才能起作用 */
    width: 100%;
    /* 最大显示两行 */
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-word;
    overflow: hidden;
    user-select: none;
}

.card-title,
.card-url {
    padding-bottom: 0;
    /* 设置标题和 URL 之间的行间距为 0 像素 */
}

.card-image {
    width: 2.5rem;
    height: 2.5rem;
    object-fit: cover;
    user-select: none;
}

.card-image>img {
    border-radius: 0.5rem;
    margin-bottom: 0;
    width: 2.5rem;
    height: 2.5rem;
}
</style>
