import { Extension } from '@tiptap/core'
import { Node as ProseMirrorNode } from '@tiptap/pm/model'
import { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'
import * as Log from '@ts/utils/Logger'
import { isFastPop, trimWhitespaceChars } from '../utils/CommonUtil'
import {DEBUG_ON_WEB, getEditorName} from "@ts/EditorUtils"
import {Constants} from "@ts/utils/Constants";
import { showToast } from '../utils/DebugUtil'

export interface CharacterCountOptions {
    /**
     * The maximum number of characters of content that should be allowed. Defaults to `0`.
     */
    limit: number | null | undefined,
}

export interface CharacterCountStorage {
    /**
     * Get the number of characters for the current document.
     */
    characters: (options?: { node?: ProseMirrorNode; mode?: 'textSize' | 'nodeSize' }) => number

    /**
     * Get the number of words for the current document.
     */
    words: (options?: { node?: ProseMirrorNode }) => number
}

export const CharacterCount = Extension.create<CharacterCountOptions, CharacterCountStorage>({
    name: 'characterCount',

    addOptions() {
        return {
            limit: null,
        }
    },

    addStorage() {
        return {
            characters: () => 0,
            words: () => 0,
        }
    },

    onBeforeCreate() {
        this.storage.characters = options => {
            const node = options?.node || this.editor.state.doc
            const text = node.textBetween(0, node.content.size, undefined, '')
            if (getEditorName(this.editor) === Constants.TIPTAP_TITLE_CLASS_NAME) {
                return text.length
            } else {
                return trimWhitespaceChars(text).length
            }
        }

        this.storage.words = options => {
            const node = options?.node || this.editor.state.doc
            const text = node.textBetween(0, node.content.size, '', '')
            const words = text.split(' ').filter(word => word !== '')

            return words.length
        }
    },

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('characterCount'),
                filterTransaction: (transaction, state) => {
                    if (transaction.docChanged) {
                        const contentLimit = this.options.limit
                        const newTotalSize = this.storage.characters({ node: transaction.doc })
                        if (newTotalSize <= contentLimit) {
                            return true
                        } else {
                            onContentOverflow()
                            return false
                        }
                    }
                    return true
                },
            }),
        ]
    },
})

function onContentOverflow() {
    if (!isFastPop()) {
        if (DEBUG_ON_WEB) {
            showToast("已超过输入上限")
        } else {
            (window as any).injectedObject?.onShowReachTextLimitToast()
        }
    }
}