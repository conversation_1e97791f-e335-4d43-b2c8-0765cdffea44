import { getNodeType, getSchemaTypeNameByName, NodeRange, objectIncludes } from "@tiptap/vue-3"
import { Editor } from '@tiptap/core'
import { RichTextToolsConstant } from "@/ts/richtext/RichTextToolsConstant"
import { getEditorName } from "@/ts/EditorUtils"
import { Constants } from "@/ts/utils/Constants"
import { isBlockquoteNode, isHeadingNode, isParagrahNode, isTableNode } from "./nodeHelper"
import { CellSelection } from '@tiptap/pm/tables';
import { isSelectionInSameTable } from "@ts/custom/helpers";

export function isHeadingActive(editor: Editor): boolean {
    const editorName = getEditorName(editor)
    const isContentEditor = editorName == Constants.TIPTAP_CONTENT_CLASS_NAME
    let result = false
    if (!isContentEditor) {
        result = true
    } else {
        const activeTextStyle = getActiveTextStyle(editor)
        if (activeTextStyle == RichTextToolsConstant.TEXT_STYLE_TITLE
            || activeTextStyle == RichTextToolsConstant.TEXT_STYLE_SUBTITLE
            || activeTextStyle == RichTextToolsConstant.TEXT_STYLE_SMALL_TITLE) {
            result = true
        }
    }
    return result
}

export function getActiveTextStyle(editor: Editor) {
    if (isTextStyleActive(editor, 'paragraph', { multiHeading: "1" })) {
        return RichTextToolsConstant.TEXT_STYLE_TITLE
    } else if (isTextStyleActive(editor, 'paragraph', { multiHeading: "2" })) {
        return RichTextToolsConstant.TEXT_STYLE_SUBTITLE
    } else if (isTextStyleActive(editor, 'paragraph', { multiHeading: "3" })) {
        return RichTextToolsConstant.TEXT_STYLE_SMALL_TITLE
    } else if (isTextStyleActive(editor, 'blockquote')) {
        return RichTextToolsConstant.TEXT_STYLE_BLOCK_QUOTE
    } else if (isTextStyleActive(editor, 'paragraph', { multiHeading: null })) {
        return RichTextToolsConstant.TEXT_STYLE_BODY_TEXT
    } else {
        return RichTextToolsConstant.TEXT_STYLE_NONE
    }
}

function isTextStyleActive(editor: Editor, name: string, attributes: Record<string, any> = {}): boolean {
    const { state } = editor
    const schemaType = getSchemaTypeNameByName(name, state.schema)
    if (schemaType === 'node') {
        const nodeType = getNodeType(name, state.schema)
        if (nodeType) {
            const { empty, from, to } = state.selection
            const nodeRanges: NodeRange[] = []
            const textNodeRanges: NodeRange[] = []
            state.doc.nodesBetween(from, to, (node, pos) => {
                const relativeFrom = Math.max(from, pos)
                const relativeTo = Math.min(to, pos + node.nodeSize)
                if (isTableNode(node)) {
                    return false
                } else if (isHeadingNode(node) || isParagrahNode(node)) {
                    nodeRanges.push({
                        node,
                        from: relativeFrom,
                        to: relativeTo,
                    })
                    // heading和paragraph 到内部文本节点的深度为1，因此使用 <= 1判断
                    if (node.childCount == 0 || (((relativeTo - relativeFrom) <= 1) && ((relativeFrom == pos) || (relativeTo == pos + node.nodeSize)))) {
                        // childCount为0时或只选中起始或结束节点，此时无法正常遍历段落里面的文本节点，但是选区范围已经包括了该节点，因此需要构造一个虚拟的文本节点方便后续判断
                        textNodeRanges.push({
                            node,
                            from: relativeFrom,
                            to: relativeTo,
                        })
                        return false
                    }
                } else if (isBlockquoteNode(node)) {
                    nodeRanges.push({
                        node,
                        from: relativeFrom,
                        to: relativeTo,
                    })
                    //blockquote 起始或结束点到内部文本节点的深度为2，因此使用 <= 2判断
                    if (node.childCount == 0 || (((relativeTo - relativeFrom) <= 2) && ((relativeFrom == pos) || (relativeTo == pos + node.nodeSize)))) {
                        // childCount为0时或只选中起始或结束节点，此时无法正常遍历段落里面的文本节点，但是选区范围已经包括了该节点，因此需要构造一个虚拟的文本节点方便后续判断
                        textNodeRanges.push({
                            node,
                            from: relativeFrom,
                            to: relativeTo,
                        })
                    } else {
                        const relativeFrom = Math.max(from, pos)
                        const relativeTo = Math.min(to, pos + node.nodeSize - 2)
                        textNodeRanges.push({
                            node,
                            from: relativeFrom,
                            to: relativeTo,
                        })
                    }
                    return false
                } else if (node.isText) {
                    const relativeFrom = Math.max(from, pos)
                    const relativeTo = Math.min(to, pos + node.nodeSize)
                    textNodeRanges.push({
                        node,
                        from: relativeFrom,
                        to: relativeTo,
                    })
                }
            })
            const matchedNodeRanges = nodeRanges.filter(nodeRange => {
                return nodeType.name === nodeRange.node.type.name
            }).filter(
                nodeRange => objectIncludes(nodeRange.node.attrs, attributes, { strict: false })
            )
            if (empty) {
                return !!matchedNodeRanges.length
            } else if (matchedNodeRanges.length == 0) {
                return false
            }
            return textNodeRanges.every(textNodeRange => {
                return matchedNodeRanges.some(headingNodeRange => {
                    return textNodeRange.from >= headingNodeRange.from && textNodeRange.to <= headingNodeRange.to
                });
            });
        }
    }
    return false
}

// 获取表格背景色
export function getTableCellBakcground(editor: Editor) {
    const defaultBackground = 'default'
    if(!isSelectionInSameTable(editor.state)) return defaultBackground
    const { selection, doc } = editor.state
    const { ranges } = selection
    let background = ranges[0].$from.node(3)?.attrs?.background as string
    if(selection instanceof CellSelection) {
        for(let i = 1; i < ranges.length; i++) {
            if(ranges[i].$from.node(3)?.attrs?.background !== background) {
                background = defaultBackground
                break
            }
        }
    } else {
        const { from, to } = selection
        for(let i = from; i < to; i++) {
            const node = doc.resolve(i).node(3)
            if(!node) continue
            if(node.attrs.background !== background){
                background = defaultBackground
                break
            }
        }
    }
    return background.startsWith('var(') ? background.split('--')[1].split('_').slice(0, 2).join('_') : defaultBackground
}
