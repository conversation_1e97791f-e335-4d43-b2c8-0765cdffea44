/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : IUndoManager.kt
 * Description    : IUndoManager.kt
 * Version        : 1.0
 * Date           : 2023/9/4
 * Author         : <PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/9/4         1.0           create
 */
package com.oplus.notes.webview.container.api

interface IUndoManager {
    fun setWebViewContainer(webViewContainer: IWebViewContainer?) {}
    fun undo() {}

    fun redo() {}

    fun isUndoAvailable(): Boolean = true

    fun isRedoAvailable(): Boolean = true

    fun setUndoAvailable(available: Boolean) {}
    fun setRedoAvailable(available: Boolean) {}
}