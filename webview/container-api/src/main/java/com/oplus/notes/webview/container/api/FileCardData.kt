/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : FileCardData.kt
 * Description    : FileCardData.kt
 * Version        : 1.0
 * Date           : 2024/6/19
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/5         1.0           create
 */
package com.oplus.notes.webview.container.api

import androidx.annotation.Keep
import org.json.JSONArray
import org.json.JSONObject

@Keep
data class FileCardData(
    val src: String,
    val attachId: String,
    val docAttachId: String,
    val fileName: String? = null,
    val filePath: String? = null,
    val fileSize: Long = 0,
    val fileDate: String? = null,
    val fileType: String? = null,
    val fileUrl: String? = null,
    val searchList: MutableList<String>? = null,
    val searchAttachId: String? = null,
    val picWidth: Int = 0,
    val picHeight: Int = 0,
    val docThumbnail: String? = null
) {
    fun toJsonObject(): JSONObject {
        return JSONObject().apply {
            put("src", src)
            put("attachId", attachId)
            put("docAttachId", docAttachId)
            put("fileName", fileName)
            put("filePath", filePath)
            put("fileSize", fileSize)
            put("fileDate", fileDate)
            put("fileType", fileType)
            put("fileUrl", fileUrl)
            if (!searchList.isNullOrEmpty()) {
                put("searchList", JSONArray(searchList).toString())
                put("searchAttachId", searchAttachId)
            }
            put("docThumbnail", docThumbnail)
        }
    }
}
