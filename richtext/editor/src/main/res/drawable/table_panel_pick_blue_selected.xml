<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 外扩边框 -->
    <item>
        <shape>
            <solid android:color="@color/transparent" />
            <corners android:radius="@dimen/dp_12" />
            <stroke
                android:width="@dimen/table_color_out_border"
                android:color="@color/real_color_dark_blue" />
        </shape>
    </item>

    <!-- 内部背景 -->
    <item android:left="@dimen/dp_3" android:top="@dimen/dp_3" android:right="@dimen/dp_3" android:bottom="@dimen/dp_3">
        <shape>
            <solid android:color="@color/real_color_dark_blue" />
            <corners android:radius="@dimen/dp_10" />
        </shape>
        <stroke
            android:width="@dimen/table_color_border"
            android:color="@color/real_color_border_blue" />
    </item>

</layer-list>