/****************************************************************
 ** Copyright (C), 2010-2023, Oplus Mobile Comm Corp., Ltd.
 ** File:  - HtmlTagTransformManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/12/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  lvhuachuan   2022/12/14     1.0            add file
 ****************************************************************/

package com.oplus.richtext.transform.trans.tags

import org.jsoup.nodes.Element

interface ITagTransform {
    fun toHtmlText(node: Element, htmlText: StringBuilder, htmlIndex: Int)

    fun toRawText(node: Element, rawText: StringBuilder, rawIndex: Int)
}