<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Even though these two point to the same resource, have two states so the drawable will invalidate itself when coming out of pressed state. -->
    <item android:state_focused="true"
        android:state_enabled="false" android:state_pressed="true"
        android:drawable="@android:color/transparent" />
    <item android:state_focused="true"
        android:state_enabled="false"
        android:drawable="@drawable/note_folder_list_selector_background_disabled" />
    <item android:state_focused="true"
        android:state_pressed="true"
        android:drawable="@drawable/note_folder_list_selector_background_transition" />
    <item android:state_focused="false"
        android:state_pressed="true"
        android:drawable="@drawable/note_folder_list_selector_background_transition" />
    <item android:state_focused="true"
        android:drawable="@color/note_folder_list_selector_color_pressed" />
    <item android:drawable="@android:color/transparent" />
</selector>