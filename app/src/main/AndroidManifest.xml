<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.oplus.note"
    android:versionCode="150050008"
    android:versionName="15.5.8">

    <uses-sdk tools:overrideLibrary="com.oplus.vfxsdk.magicediteffect,com.oplus.vfxsdk.rsview,com.oplus.coe.common,com.oplus.support.dmp.aiask,com.oplus.dmp.sdk.aiask,com.oplus.dmp.sdk.common"/>
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <!--add for getInstalledPackages and getPackageInfo-->
    <queries>
        <package android:name="com.oplus.securitypermission"/>
        <package android:name="com.nearme.sync" />
        <package android:name="com.coloros.cloud" />
        <package android:name="com.coloros.ocrscanner" />
        <package android:name="com.android.settings" />
        <package android:name="com.heytap.speechassist" />
        <package android:name="com.coloros.speechassist" />
        <package android:name="com.heytap.cloud" />
        <package android:name="com.heytap.themestore" />
        <package android:name="com.nearme.themespace" />
        <package android:name="com.nearme.themestore" />
        <package android:name="com.oplus.themestore" />
        <package android:name="com.oplus.ai.assistant" />
        <package android:name="cn.wps.moffice.lite"/>
        <package android:name="com.oplus.supertextinput"/>
        <package android:name="com.coloros.translate.engine"/>
        <package android:name="com.coloros.accessibilityassistant"/>
        <package android:name="com.example.audioplayerdev" />
        <package android:name="com.coloros.sceneservice" />
        <package android:name="com.coloros.soundrecorder" />
        <package android:name="com.oneplus.soundrecorder" />
        <package android:name="andes.oplus.documentsreader"/>
        <package android:name="com.coloros.filemanager"/>
        <package android:name="com.oplus.filemanager"/>

        <!--隐私政策-->
        <intent>
            <action android:name="com.oplus.bootreg.activity.statementpage" />
        </intent>
        <intent>
            <action android:name="com.coloros.bootreg.activity.statementpage" />
        </intent>
        <!--涂鸦手写笔设置-->
        <intent>
            <action android:name="com.oplus.ipemanager.action.pencil_setting_from_notes" />
        </intent>
        <!--云服务设置页-->
        <intent>
            <action android:name="com.coloros.cloud.action.NOTE_SETTING" />
        </intent>
        <intent>
            <action android:name="com.heytap.cloud.action.NOTE_SETTING" />
        </intent>
        <!--设置字体-->
        <intent>
            <action android:name="com.heytap.themestore.action.FONT_FOR_NOTE"/>
        </intent>
        <!--查看图片-->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:mimeType="image/*" />
        </intent>

        <!--文件管理器打开导出doc目录-->
        <intent>
            <action android:name="oppo.filemanager.intent.action.BROWSER_FILE"/>
        </intent>
        <intent>
            <action android:name="oplus.intent.action.filemanager.BROWSER_FILE"/>
        </intent>
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.coloros.cloud.ACCESS_SYNC_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="com.coloros.speechassist.permission.SPEECH_SERVICE" />
    <uses-permission android:name="oppo.permission.cloud.ACCESS_CLOUD" />
    <uses-permission android:name="oppo.permission.cloud.GET_CLOUD_INFO" />
    <uses-permission android:name="heytap.permission.cloud.ACCESS_CLOUD" />
    <uses-permission android:name="heytap.permission.cloud.ACCESS_SYNC_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--组件安全权限 参考文档：   https://odocs.myoas.com/docs/ywqQtYVpkWjx3TJK -->
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="com.oplus.permission.safe.SAU" />
    <uses-permission android:name="com.oplus.permission.safe.APP_MANAGER" />
    <uses-permission android:name="com.oplus.permission.safe.PROTECT" />
    <uses-permission android:name="com.oplus.permission.safe.WINDOW" />
    <uses-permission android:name="com.oplus.permission.safe.AUTHENTICATE" />
    <uses-permission android:name="com.oplus.permission.safe.SECURITY" />
    <uses-permission android:name="com.oplus.permission.safe.SETTINGS" />
    <uses-permission android:name="com.oplus.permission.safe.BACKUP"/>
    <uses-permission android:name="com.oplus.permission.safe.PRIVATE" />

    <uses-permission android:name="net.oneplus.launcher.permission.READ_BACKUP"/>
    <uses-permission android:name="net.oneplus.launcher.permission.WRITE_BACKUP"/>

    <uses-permission android:name="com.oplus.ipemanager.permission.receiver.DOUBLE_CLICK"/>

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <uses-permission android:name="android.permission.TURN_SCREEN_ON"/>

    <!--跳转至语音转文字个人信息保护政策-->
    <uses-permission android:name="com.coloros.accessibilityassistant.permission.SUBTITLE_SPLIT_ACTIVITY"/>
    <!--备忘录搬家调用便签解析htmlService 自定义权限-->
<!--    <permission android:name="com.oplus.note.permission.START_NOTE_SERVICE"/>-->

    <application
        android:name="com.nearme.note.MyApplication"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:icon="@drawable/ic_launcher_nearme_note"
        android:installLocation="internalOnly"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:persistent="false"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme.DayNight"
        tools:replace="android:allowBackup,android:theme,android:name">
        <!--便签是否支持回收站密码验证功能 参考文档 https://odocs.myoas.com/docs/L9kBMPW170h9LXqK?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1-->
        <meta-data
            android:name="is_recycle_bin_support_encrypt"
            android:value="1" />
        <meta-data
            android:name="QuickNotesToolVersionForSidebar"
            android:value="1" />
        <meta-data
            android:name="summary_note_version"
            android:value="1" />
        <meta-data
            android:name="record_summary_support"
            android:value="2" />
        <meta-data
            android:name="quick_note_version"
            android:value="1" />
        <meta-data
            android:name="com.oplus.aiunit.auth_style"
            android:value="0" />
        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="com.oplus.statistics"
            android:required="false" />
        <meta-data
            android:name="theme_version_metadata"
            android:value="710.002.000" />
        <meta-data
            android:name="versionCommit"
            android:value="${versionCommit}" />
        <meta-data
            android:name="versionDate"
            android:value="${versionDate}" />
        <meta-data
            android:name="AppCode"
            android:value="2001" />
        <meta-data
            android:name="color.support.options"
            android:value="@string/color_support_value" />
        <meta-data
            android:name="OplusPermissionKey"
            android:value="${permission_key}"/>
        <meta-data
            android:name="OppoPermissionKey"
            android:value="${permission_key}"/>

        <!--通知权限弹窗内销自定义描述文案  https://odocs.myoas.com/docs/vVqRJoQp2BTldEqy?lang=zh-CN&zone=%2B8&mobileMsgShowStyle=1&pcMsgShowStyle=1-->
        <meta-data
            android:name="android.permission-group.NOTIFICATIONS"
            android:resource="@string/dialog_open_permission_content" />

        <!-- 表明该应用支持跟随全局颜色切换:https://hio.oppo.com/app/ozone/ColorOSDev/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=157892934_157799345&folder_id=39203 -->
        <meta-data
            android:name="color_material_enable"
            android:value="true" />

        <meta-data
            android:name="com.oplus.note.activity.insert_note"
            android:value="true" />

        <!--The description of this token is here: https://doc.myoas.com/pages/viewpage.action?pageId=389176368-->
        <meta-data
            android:name="OplusAppCompatibilityToken"
            android:value="22,22"/>

        <!-- appcard -->
        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBFAiEAjgdUMeDXVjYUdd/pI1uH07sRye+AqZl4L23OpgFsEXwCIFq0tRpub2sxvyVwDuUyvtT3GCeBzk9Ho+OoLSMxt5zUamX0bQ==" />

        <!--    AppPlatform 鉴权码技术方案：https://odocs.myoas.com/docs/R13j8rW7N4FpDek5    -->
        <!--    AppPlatform 鉴权码申请：https://odocs.myoas.com/forms/e1Az4VMPK ghLx2qW/fill    -->
        <!--    内销鉴权码     -->
        <meta-data
            android:name="OsdkSecurityCode"
            android:value="ATBFAiEA377Ed8Q6OMJ9tnMwn0Roiig8Hqta41Z8vHSFnB2rl9cCIE7VY0FWoPhPTBJ8w0QjdAYyPqQlKhl15yjXqeyU04tiZolOck9wbHVzQWN0aXZpdHlNYW5hZ2VyLmdldEN1cnJlbnRVc2VyIwAAAA==" />

        <!--    TBL Modification：定制便签特定逻辑需要     -->
        <meta-data android:name="tbl.webkit.APPLICATION_TYPE" android:value="NOTEAPP"/>

        <!-- permission control in code -->
        <provider
            android:name="com.nearme.note.db.NotesProvider"
            android:authorities="${applicationId}.notesprovider;com.nearme.note"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <meta-data
                android:name="com.nearme.note.db.insert_todo"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.insert_text_note"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.insert_rich_note"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.insert_pic_text_note"
                android:value="true" />
            <meta-data
                android:name="com.nearme.note.db.repeat_todo"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.delete_text_note"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.update_todo"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.query_notes_no_limit"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_allow_create_time"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_allow_folder_id"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_api_version"
                android:value="2"/>
        </provider>

        <provider
            android:name="com.nearme.note.cardwidget.provider.NoteCardWidgetProvider"
            android:authorities="${applicationId}.cardwidget"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider.array"
                android:resource="@array/note_appcard_array" />
            <meta-data
                android:name="android.card.todo.middle.provider"
                android:resource="@xml/todo_middle_appcard" />

            <meta-data
                android:name="android.card.todo.large.provider"
                android:resource="@xml/todo_large_appcard" />

            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/note_appcard" />
        </provider>

        <!-- permission control in code -->
        <provider
            android:name="com.oplus.cloud.sync.note.SyncDataProvider"
            android:authorities="${applicationId}.sync;com.nearme.note.sync"
            android:exported="false" />

        <activity
            android:name="com.nearme.note.main.MainActivity"
            android:configChanges="keyboardHidden|keyboard|navigation|orientation|smallestScreenSize|screenLayout|screenSize"
            android:hardwareAccelerated="true"
            android:resizeableActivity="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:exported="true"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="action.nearme.note.allnote" />
                <action android:name="action.nearme.note.home" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.oplus.note.action.edit_todo" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
            <meta-data
                android:name="oldComponentName"
                android:resource="@array/OplusNoteOldComponentName" />
            <meta-data
                android:name="oldComponentNameVersion"
                android:value="1" />
        </activity>

        <!-- LK ADD BEGIN -->
        <!--语音助手是指定包名启动笔记页面的，需要重定向activity到富文本编辑器 -->
        <activity-alias
            android:name="com.nearme.note.activity.edit.NoteViewEditActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:resizeableActivity="true"
            android:screenOrientation="behind"
            android:targetActivity="com.nearme.note.activity.richedit.NoteViewRichEditActivity"
            android:theme="@style/AppBaseTheme.NoActionBar"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />

        <activity
            android:name="com.nearme.note.paint.PaintActivity"
            android:configChanges="keyboardHidden|navigation|orientation|screenLayout|smallestScreenSize"
            android:screenOrientation="behind"/>

        <activity
            android:name="com.nearme.note.setting.SettingsSyncSwitchActivity"
            android:exported="true"
            android:icon="@drawable/ic_launcher_nearme_note"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="oplus.intent.action.NOTE_CLOUD_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.nearme.note.paint.QuickPaintActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:showOnLockScreen="true"
            android:showWhenLocked="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:taskAffinity="com.oplus.note.quick_paint_task"
            tools:ignore="NonResizeableActivity">
            <intent-filter>
                <action android:name="com.oplus.note.quick_paint" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.nearme.note.paint.QuickPaintShareActivity"
            android:excludeFromRecents="true"
            android:resizeableActivity="false"
            android:taskAffinity="com.oplus.note.quick_paint_task"
            tools:ignore="NonResizeableActivity" />

        <activity
            android:name="com.nearme.note.activity.richedit.NoteViewRichEditActivity"
            android:configChanges="keyboardHidden|keyboard|orientation|screenSize|mcc|mnc|navigation|screenLayout|smallestScreenSize|fontScale"
            android:resizeableActivity="true"
            android:exported="true"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.DetailPage">
            <intent-filter>
                <!-- 文本笔记 -->
                <action android:name="action.nearme.note.textnote" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="action.nearme.note.textnote.voice" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <activity
            android:name="com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|mcc|mnc|navigation|screenLayout|smallestScreenSize"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="@string/quick_note_name"
            android:launchMode="singleTask"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:resizeableActivity="true"
            android:screenOrientation="behind"
            android:taskAffinity="com.oplus.note.quick_note"
            android:theme="@style/AppTheme.DayNight.DetailPage"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />

        <activity
            android:name="com.nearme.note.activity.richedit.TransparentActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="@string/quick_note_name"
            android:taskAffinity="com.oplus.note.quick_note"
            android:theme="@style/AppTheme.Translucent">
            <intent-filter>
                <!-- 速记小窗新启动方式 -->
                <action android:name="action.nearme.note.quicknote" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.PROCESS_TEXT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>

            <intent-filter>
                <action android:name="action.nearme.note.add.quicknote" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="action.nearme.note.setting.screenon" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.oplus.note.action.OPEN_COLLECTION_NOTE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="com.oplus.note.open.support_version"
                android:value="3" />
        </activity>

        <activity
            android:name="com.nearme.note.activity.richedit.TransparentNoTaskAffinityActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/AppTheme.Translucent">
            <intent-filter>
                <action android:name="com.oplus.note.action.OPEN_NOTE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.nearme.note.activity.richedit.SpeechTransparentActivity"
            android:excludeFromRecents="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:exported="true">
            <intent-filter>
                <action android:name="action.nearme.note.record.voice" />
                <action android:name="action.nearme.note.play.voice" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.nearme.note.activity.richedit.SummaryTransparentActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="@string/quick_note_name"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:targetActivity="com.nearme.note.activity.richedit.TransparentActivity"
            android:taskAffinity="com.oplus.note.quick_note"
            android:theme="@style/AppTheme.Translucent">
            <intent-filter>
                <action android:name="com.oplus.note.action.OPEN_SUMMARY_NOTE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.nearme.note.activity.richedit.QuickNoteListActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|mcc|mnc|navigation|screenLayout|smallestScreenSize"
            android:resizeableActivity="true"
            android:exported="true"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.DetailPage">

        </activity>

        <activity
            android:name="com.nearme.note.activity.richedit.ShareActivity"
            android:theme="@style/AppTheme.Translucent"
            android:label="@string/save_to_collection"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboardHidden|uiMode|layoutDirection"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/xml" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/html" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/txt" />
            </intent-filter>
            <intent-filter>
                <!-- 浏览器分享 -->
                <action android:name="action.nearme.note.quickshare" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="audio/mpeg" />
                <data android:mimeType="audio/amr" />
                <data android:mimeType="audio/amr-wb" />
                <data android:mimeType="audio/wav" />
                <data android:mimeType="audio/x-wav" />
                <data android:mimeType="audio/aac" />
                <data android:mimeType="audio/mp4" />
                <data android:mimeType="audio/mp3" />
                <data android:mimeType="audio/x-m4a" />
                <data android:mimeType="audio/aac-adts" />
                <data android:mimeType="audio/ogg" />
                <data android:mimeType="audio/x-ape" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="video/mp4" />
                <data android:mimeType="video/quicktime" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="application/pdf" />
                <data android:mimeType="application/msword" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                <data android:mimeType="application/vnd.ms-excel" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                <data android:mimeType="image/vnd.adobe.photoshop" />
                <data android:mimeType="application/octet-stream" />
                <data android:mimeType="application/xmind" />
                <data android:mimeType="application/vnd.ms-visio.drawing" />
                <data android:mimeType="application/pgp-keys" />
                <data android:mimeType="image/x-photoshop" />
                <data android:mimeType="image/vnd.dwg" />
                <data android:mimeType="application/postscript" />
                <data android:mimeType="application/vnd.visio" />
                <data android:mimeType="application/vnd.apple.pages" />
                <data android:mimeType="application/vnd.apple.numbers" />
                <data android:mimeType="text/markdown" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="audio/mpeg" />
                <data android:mimeType="audio/amr" />
                <data android:mimeType="audio/amr-wb" />
                <data android:mimeType="audio/wav" />
                <data android:mimeType="audio/x-wav" />
                <data android:mimeType="audio/aac" />
                <data android:mimeType="audio/mp4" />
                <data android:mimeType="audio/mp3" />
                <data android:mimeType="audio/x-m4a" />
                <data android:mimeType="audio/aac-adts" />
                <data android:mimeType="audio/ogg" />
                <data android:mimeType="audio/x-ape" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="video/mp4" />
                <data android:mimeType="video/quicktime" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="application/pdf" />
                <data android:mimeType="application/msword" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                <data android:mimeType="application/vnd.ms-excel" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                <data android:mimeType="application/vnd.ms-powerpoint" />
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                <data android:mimeType="image/vnd.adobe.photoshop" />
                <data android:mimeType="application/octet-stream" />
                <data android:mimeType="application/xmind" />
                <data android:mimeType="application/vnd.ms-visio.drawing" />
                <data android:mimeType="application/pgp-keys" />
                <data android:mimeType="image/x-photoshop" />
                <data android:mimeType="image/vnd.dwg" />
                <data android:mimeType="application/postscript" />
                <data android:mimeType="application/vnd.visio" />
                <data android:mimeType="application/vnd.apple.pages" />
                <data android:mimeType="application/vnd.apple.numbers" />
                <data android:mimeType="text/markdown" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <!-- 新增对text/html的支持 -->
            <meta-data
                android:name="com.oplus.note.share.support_rich_note"
                android:value="true" />

            <meta-data
                android:name="com.oplus.note.share.support_favorite_version"
                android:value="1" />
        </activity>

        <activity
            android:name="com.nearme.note.setting.opensourcelicense.OpenSourceActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.bigmodel.BigModelInfoActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.SettingsInformationActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.SettingsInformationDetailActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.SettingsAboutActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.SettingPrivacyActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.nearme.note.setting.UserInformationListActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"/>

        <activity
            android:name="com.nearme.note.activity.edit.SaveImageAndShare"
            android:label="@string/rich_note_label"
            android:resizeableActivity="true"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:screenOrientation="behind"
            android:configChanges="keyboardHidden|navigation|smallestScreenSize|orientation"
            android:uiOptions="splitActionBarWhenNarrow">

        </activity>

        <activity
            android:name="com.nearme.note.activity.edit.ShareImageHorizontalScrollActivity"
            android:label="@string/rich_note_label"
            android:resizeableActivity="true"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:screenOrientation="behind"
            android:configChanges="keyboardHidden|navigation|smallestScreenSize|orientation"
            android:uiOptions="splitActionBarWhenNarrow">

        </activity>
        <activity
            android:name="com.nearme.note.setting.SettingsActivity"
            android:icon="@drawable/ic_launcher_nearme_note"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="com.android.settings.APP_PRIORITY"
                android:value="40021" />
        </activity>
        <!-- LK ADD END -->

        <activity
            android:name="com.nearme.note.appwidget.todowidget.TodoSettingActivity"
            android:configChanges="keyboardHidden|screenSize|mcc|mnc|navigation|screenLayout"
            android:exported="true"
            android:launchMode="singleInstance"
            android:resizeableActivity="false"
            android:noHistory="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize">
            <intent-filter>
                <action android:name="action.oplus.note.todo.setting" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="upgrade_product_code"
            android:value="2002" />

        <meta-data
            android:name="recommand_cat_code"
            android:value="2183" />

        <meta-data
            android:name="recommand_product_code"
            android:value="187563" />
        <!--        add feedback product code-->
        <meta-data
            android:name="feedback_product_code"
            android:value="20198" />

        <receiver
            android:name="com.nearme.note.util.UCReceiver"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="oppo.intent.action.usercenter.ACCOUNT_LOGOUT" />
            </intent-filter>
        </receiver>

        <!-- coloros SyncNoteServive -->
        <service
            android:name="com.oplus.cloud.sync.note.SyncNoteServive"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>
        <!-- heytap SyncNoteServive -->

        <service
            android:name="com.heytap.cloud.sync.note.SyncNoteServive"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.heytap.cloud.REMOTE_SYNC_MODULE_NOTE" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <service
            android:name="com.nearme.note.thirdlog.service.NoteService"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:exported="true">
            <intent-filter>
                <action android:name="com.oplus.note.action.NOTE_SERVICE"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <service
            android:name="com.nearme.note.thirdlog.service.NoteOffLineRetryService"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:foregroundServiceType="dataSync"
            android:exported="false">
            <intent-filter>
                <action android:name="com.oplus.note.action.NOTE_SERVICE"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <service
            android:name="com.nearme.note.thirdlog.service.AICollectService"
            android:exported="false"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:foregroundServiceType="dataSync">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <activity
            android:name="com.nearme.note.view.TodoModalActivity"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.Translucent"
            android:windowSoftInputMode="adjustResize"
            android:resizeableActivity="false"
            tools:ignore="LockedOrientationActivity"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.oplus.notes.action.edit_todo"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:name="com.nearme.note.ocr.OcrConverterActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|mcc|mnc|navigation|screenLayout|smallestScreenSize|uiMode"
            android:resizeableActivity="true"
            android:exported="false"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:taskAffinity="com.oplus.note.ocr"
            android:excludeFromRecents="true"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.BlueDetailPage">
        </activity>

        <activity
            android:name="com.nearme.note.setting.privacypolicy.PrivacyPolicyActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:uiOptions="splitActionBarWhenNarrow" >
            <intent-filter>
                <action android:name="com.nearme.note.action.privacypolicy" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.nearme.note.setting.privacypolicy.PersonalInforActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:uiOptions="splitActionBarWhenNarrow" >
            <intent-filter>
                <action android:name="com.nearme.note.action.personalinfor" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.nearme.note.setting.privacypolicy.NoteUserAgreementActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.MainStyle"
            android:uiOptions="splitActionBarWhenNarrow" >

            <intent-filter>
                <action android:name="com.nearme.note.action.userAgreement" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.nearme.note.activity.richedit.thirdlog.ThirdLogDetailActivity"
            android:configChanges="keyboardHidden|orientation|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.ThirdLogDetailPage"
            android:uiOptions="splitActionBarWhenNarrow" />

        <activity
            android:name="com.nearme.note.setting.NotebookSyncSwitchActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow" />

        <receiver
            android:name="com.nearme.note.util.SellModeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.oppo.daydreamvideo.REQUEST_RESTORE_DATA" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.nearme.note.util.AlarmUtils$AlarmReceiver"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.nearme.note.util.SellModeService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <service
            android:name="com.nearme.note.util.SellResetService"
            android:exported="true"
            android:foregroundServiceType="shortService"
            android:permission="com.oplus.permission.safe.PROTECT">
            <intent-filter>
                <action android:name="com.coloros.note.action.SELLMODE_RESET" />
            </intent-filter>
        </service>

        <!--录制语音时，固定通知栏在最顶部-->
        <service android:name="com.nearme.note.speech.notification.NotificationForegroundService"
            android:foregroundServiceType="dataSync" />

        <!--音频文件播放时，固定通知栏在最顶部-->
        <service android:name=".audioplayer.AudioPlayNotificationService"
            android:foregroundServiceType="dataSync" />

        <receiver
            android:name="com.nearme.note.appwidget.todowidget.ToDoWidgetProvider"
            android:exported="true"
            android:label="@string/todo_widget"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:readPermission="com.android.launcher.permission.READ_SETTINGS"
            android:writePermission="com.android.launcher.permission.WRITE_SETTINGS">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
                <action android:name="com.oplus.note.action.CLICK_TODO_LIST" />
                <action android:name="com.oplus.note.action.TODO_DATA_CHANGED" />
                <action android:name="com.oplus.note.action.TODO_LAUNCHER_VISIBLE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/todo_appwidget" />
        </receiver>

        <service
            android:name="com.nearme.note.appwidget.todowidget.TodoWidgetRemoteViewService"
            android:permission="android.permission.BIND_REMOTEVIEWS" />

        <receiver
            android:name="com.nearme.note.appwidget.notewidget.NoteWidgetProvider"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:readPermission="com.android.launcher.permission.READ_SETTINGS"
            android:writePermission="com.android.launcher.permission.WRITE_SETTINGS">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
                <action android:name="com.oplus.note.action.NOTE_DATA_CHANGED" />
                <action android:name="com.oplus.note.action.NOTE_LAUNCHER_VISIBLE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/note_appwidget" />
        </receiver>

        <service
            android:name="com.nearme.note.appwidget.notewidget.NoteWidgetRemoteViewService"
            android:permission="android.permission.BIND_REMOTEVIEWS" />

        <receiver
            android:name="com.nearme.note.TheLocaleChangeReceiver"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="android.intent.action.LOCALE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.nearme.note.appwidget.notewidget.EmptyWidgetReceiver"
            android:exported="false">
        </receiver>

        <!--搬家SDK Service-->
        <service
            android:name="com.oplus.migrate.backuprestore.plugin.NotePluginService"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:exported="true">

            <meta-data

                android:name="uniqueID"
                android:value="818005">
            </meta-data>

            <meta-data
                android:name="version"
                android:value="1">
            </meta-data>

            <meta-data
                android:name="isVendorSupport"
                android:value="true">
            </meta-data>

            <meta-data
                android:name="backup_name_resId"
                android:value="@string/app_name">
            </meta-data>

            <meta-data
                android:name="servicePriority"
                android:value="2">
            </meta-data>

            <meta-data
                android:name="minSupportOsVersion"
                android:value="19">
            </meta-data>

            <intent-filter>
                <action android:name="com.coloros.br.service" />
                <action android:name="com.heytap.br.service" />
                <action android:name="com.oplus.br.service" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <service
            android:name="com.oplus.migrate.backuprestore.plugin.third.FavoriteNotePluginService"
            android:exported="true"
            android:permission="com.oplus.permission.safe.BACKUP">
            <meta-data
                android:name="uniqueID"
                android:value="1230" />
            <meta-data
                android:name="version"
                android:value="1" />
            <meta-data
                android:name="backup_name_resId"
                android:value="@string/memo_collection" />
            <meta-data
                android:name="backup_icon_resId"
                android:value="@drawable/ic_launcher_nearme_note" />
            <meta-data
                android:name="isVendorSupport"
                android:value="true" />
            <meta-data
                android:name="servicePriority"
                android:value="-1" />
            <intent-filter>
                <action android:name="com.heytap.br.service" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <service
            android:name="com.oplus.migrate.backuprestore.plugin.third.ThirdNotePluginService"
            android:exported="true"
            android:permission="com.oplus.permission.safe.BACKUP">
            <meta-data
                android:name="uniqueID"
                android:value="1720" />
            <meta-data
                android:name="isThirdSupport"
                android:value="true" />
            <meta-data
                android:name="isVendorSupport"
                android:value="true" />
            <meta-data
                android:name="version"
                android:value="3" />
            <intent-filter>
                <action android:name="com.oplus.br.service" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <provider
            android:authorities="com.oplus.note.draganddrop;${applicationId}.fileprovider;${applicationId}.draganddrop"
            android:name=".NoteFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            android:initOrder="50"
            tools:node="merge">
            <meta-data
                android:name="com.nearme.note.repoproxy.RepoProxyInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>