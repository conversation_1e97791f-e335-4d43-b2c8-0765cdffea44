/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: TodoSynManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/6
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.appwidget.WidgetUtils
import com.oplus.note.repo.todo.entity.ToDo
import com.nearme.note.model.ToDoRepository
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.todo.strategy.*
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.TodoTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.view.CloudKitInfoController
import java.util.*

class TodoSyncManager : AbsDataSyncManager(Constants.MODULE_TODO, Constants.MODULE_TODO, Constants.RECORD_TYPE_VERSION_TODO_ITEM) {

    companion object {
        private const val TAG = "TodoSyncManager"
    }

    private val transformer = TodoTransformer()
    private val mergerHelper = MergerHelper()
    private var hasNewData = false

    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(TAG, "onStartRecovery")
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        hasNewData = data != null && data.isNotEmpty()

        var deleteCount = 0
        var updateCount = 0
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd:${data?.size}")
        data?.forEach { record ->
            if (record.sysStatus == Constants.RECORD_STATUS_DELETED || record.sysStatus == Constants.RECORD_STATUS_RECYCLED) {
                deleteCount++
                // 彻底删除
                val remote = transformer.convertDeletedToToDoFrom(record)
                if (remote != null) {
                    val relate = ToDoRepository.getInstance().getByGlobalIdSync(record.sysRecordId)
                    mergerHelper.mergeWhenDelete(remote, relate)
                }
            } else {
                updateCount++
                // 新增或修改（包括删除到回收站）
                val remote = transformer.convertToToDoFrom(record)
                if (remote != null) {
                    val relate = ToDoRepository.getInstance().getByLocalIdSync(remote.localId)
                    mergerHelper.mergeWhenUpdate(remote, relate)
                    val hasAlarm = (remote.alarmTime?.time ?: 0) > System.currentTimeMillis()
                    val isCreate = relate == null && hasAlarm
                    val isUpdate = relate != null && !relate.isDelete && hasAlarm
                    AppLogger.CLOUDKIT.d(TAG, " isCreate = $isCreate isUpdate $isUpdate")
                    updateCloudKitInfoControllerValues(isCreate, isUpdate)
                }
            }
        }
        mergerHelper.flush()
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd: $updateCount, $deleteCount")
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        backUp.invoke()
        AppLogger.BASIC.d(TAG, "onRecoveryEnd")
    }

    private fun updateCloudKitInfoControllerValues(isCreate: Boolean, isUpdate: Boolean) {
        if (isCreate || isUpdate) {
            CloudKitInfoController.isNotifyIgnore = false
            CloudKitInfoController.isAlarmIgnore = false
            CloudKitInfoController.isScreenOnIgnore = false
            CloudKitInfoController.isOverlayIgnore = false
            CloudKitInfoController.isDiffIgnore = false
        }
    }

    override fun getMetaDataCount(): Int {
        return ToDoRepository.getInstance().countOfAll() - ToDoRepository.getInstance().countOf(ToDo.StatusEnum.NEW)
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(TAG, "onStartBackup")

        if (hasNewData) {
            hasNewData = false
            WidgetUtils.sendTodoDataChangedBroadcast(appContext)
        }
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {
        val dirtyTodos = ToDoRepository.getInstance().dirtyData
        AppLogger.CLOUDKIT.d(TAG, "onQueryDirtyData: ${dirtyTodos.size}")
        correctLegacyGlobalIds(dirtyTodos)
        return dirtyTodos?.mapNotNull { transformer.convertToRecordFrom(it) } ?: emptyList()
    }

    /**
     * There are some legacy data has no global id when update to Cloudkit version.
     * Add the global id here
     */
    private fun correctLegacyGlobalIds(data: List<ToDo>?) {
        val legacy = data?.filter {
            val emptyGlobalId = it.globalId == null
            if (emptyGlobalId) {
                it.globalId = UUID.randomUUID()
            }
            emptyGlobalId
        }

        if (!legacy.isNullOrEmpty()) {
            ToDoRepository.getInstance().updateAll(legacy, false)
        }
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupStart: ${data?.size}")

        // TODO observe data changed when sync todo?
    }

    override fun onPagingBackupEnd(successData: List<CloudBackupResponseRecordProxy>?, errorData: List<CloudBackupResponseErrorProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupEnd")

        // delete local data.
        successData?.filter { record ->
            record.operatorType == Constants.OPERATOR_TYPE_DELETE || record.operatorType == Constants.OPERATOR_TYPE_RECYCLE
                    || record.operatorType == Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
        }?.forEach { record ->
            ToDoRepository.getInstance().deleteByGlobalId(record.sysRecordId)
        }

        // update local data.
        successData?.filter { record ->
            record.operatorType != Constants.OPERATOR_TYPE_DELETE && record.operatorType != Constants.OPERATOR_TYPE_RECYCLE
        }?.mapNotNull { record ->
            ToDoRepository.getInstance().getByGlobalIdSync(record.sysRecordId)?.apply {
                sysVersion = record.sysVersion
                status = ToDo.StatusEnum.UNCHANGE

                alarmTimePre = alarmTime
                repeatRulePre = repeatRule
                forceReminderPre = forceReminder
            }
        }?.apply {
            ToDoRepository.getInstance().updateAll(this, false)
        }

        // TODO resolve error records
    }

    class MergerHelper {

        private var toDoRepository = ToDoRepository.getInstance()

        private val updateMergers by lazy {
            mutableListOf(
                    ToDoConditionJudgeStrategy(toDoRepository),
                    ToDoNewStrategy(toDoRepository),
                    ToDoSameContentStrategy(toDoRepository),
                    ToDoUpdateStrategy(toDoRepository),
                    ToDoConflictStrategy(toDoRepository)
            )
        }

        private val deleteMergers by lazy {
            mutableListOf(
                    ToDoDelConditionStrategy(toDoRepository),
                    ToDoDeleteStrategy(toDoRepository),
                    ToDoDeleteConflictStrategy(toDoRepository)
            )
        }

        /**
         * @return true means the given remote data handle by a valid merge strategy.
         */
        fun mergeWhenUpdate(remote: ToDo, relate: ToDo?): Boolean {
            updateMergers.forEach { strategy ->
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
            return false
        }

        /**
         * @return true means the given remote data handle by a valid merge strategy.
         */
        fun mergeWhenDelete(remote: ToDo, relate: ToDo?): Boolean {
            deleteMergers.forEach { strategy ->
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
            return false
        }

        fun flush() {
            updateMergers.forEach { it.mergeDataListBuffer() }
            deleteMergers.forEach { it.mergeDataListBuffer() }
        }
    }
}