/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - FloatingViewTouchListener
 * * Description:
 * * Version: 1.0
 * * Date : 2023/1/31
 * * Author: NieXiaokang
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * NieXiaokang 2023/1/31 1.0 create
 ******************************************************************/
package com.oplus.note.view.floatingmenu

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.Gravity
import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import android.view.SoundEffectConstants
import android.view.View
import android.view.ViewConfiguration
import android.view.accessibility.AccessibilityEvent
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.oplus.richtext.editor.view.LinkEditText
import org.jetbrains.annotations.TestOnly
import com.nearme.note.view.PressFeedbackHelper
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import java.lang.ref.WeakReference

abstract class FloatingViewTouchListener(view: View, private val mTag: Any? = null) : View.OnTouchListener {
    companion object {
        const val TAG = "FloatingViewTouchListener"
        const val LEFT_HOT_ZONE = Gravity.START
        const val MIDDLE_HOT_ZONE = Gravity.CENTER
        const val RIGHT_HOT_ZONE = Gravity.END
        private const val VIBRATE_LONG_PRESS = HapticFeedbackConstants.LONG_PRESS
        private const val SOUND_CLICK = SoundEffectConstants.CLICK
        private const val EVENT_CLICK = AccessibilityEvent.TYPE_VIEW_CLICKED
        private const val EVENT_LONG_PRESS = AccessibilityEvent.TYPE_VIEW_LONG_CLICKED
        private const val MSG_LONG_PRESS = 1000
        private const val MSG_DRAG_PRESS = 1001
        private const val MSG_SINGLE_CLICK = 1002
        private const val DRAG_TIMEOUT = 1000L
        private const val HOT_CLICK_WIDTH_RATIO = 0.3F
    }

    private val mView: WeakReference<View>
    private var mCurrentDownEvent: MotionEvent? = null
    private val mLongPressTimeout = ViewConfiguration.getLongPressTimeout().toLong()
    private val mClickTimeout = mLongPressTimeout
    private var isStylusTouch = false

    /**
     * 滑动的限定值，取的平方，比较的时候也需用平方对比
     */
    private val mTouchSlopSquare: Float
    private var mLongPressed = false
    private val mFeedbackHelper: PressFeedbackHelper

    /**
     * The number of pointers of data contained in touch event
     */
    private var touchPoints = 0

    init {
        mView = WeakReference(view)
        val square = view.resources.getDimension(R.dimen.press_feedback_max_change)
        mTouchSlopSquare = square * square
        mFeedbackHelper = PressFeedbackHelper(view, intercept = false, isNeedInitListener = false)
    }

    @get:TestOnly
    val mHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            getView()?.let {
                when (msg.what) {
                    MSG_LONG_PRESS -> {
                        mLongPressed = true
                        sendEmptyMessageDelayed(MSG_DRAG_PRESS, DRAG_TIMEOUT)
                        perform(MSG_LONG_PRESS)
                        onLongPress(it)
                    }

                    MSG_DRAG_PRESS -> {
                        mFeedbackHelper.onActionUp()
                        if (touchPoints > 1) {
                            AppLogger.BASIC.d(TAG, "touch points count > 1 ,can`t drag.")
                            return@let
                        }
                        onDragPress(it) { dragSuccess ->
                            if (dragSuccess) {
                                perform(MSG_DRAG_PRESS)
                            }
                        }
                    }

                    MSG_SINGLE_CLICK -> {
                        perform(MSG_SINGLE_CLICK)
                        if (msg.obj is Float) {
                            val x = msg.obj as Float
                            if (x < it.layoutParams.width * HOT_CLICK_WIDTH_RATIO) {
                                onClick(it, LEFT_HOT_ZONE)
                                return
                            } else if (x > it.layoutParams.width * (1 - HOT_CLICK_WIDTH_RATIO)) {
                                onClick(it, RIGHT_HOT_ZONE)
                                return
                            }
                        }
                        onClick(it, MIDDLE_HOT_ZONE)
                    }
                }
            }
        }
    }

    private fun getView(): View? {
        return mView.get()
    }

    override fun onTouch(v: View, event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                touchPoints = event.pointerCount
                isStylusTouch = false
                for (i in 0 until event.pointerCount) {
                    isStylusTouch = event.getToolType(i) == MotionEvent.TOOL_TYPE_STYLUS
                }
                mTag?.let {
                    if (it is RecyclerView) {
                        val focusedChild = it.layoutManager?.focusedChild
                        if ((focusedChild != null) && (focusedChild is LinkEditText)) {
                            focusedChild.stopTextContextActionMode()
                        } else if (focusedChild is FrameLayout) {
                            val editText = focusedChild.findViewById<LinkEditText?>(R.id.text)
                            if (editText is LinkEditText) {
                                editText.stopTextContextActionMode()
                            }
                        }
                    }
                }
                mCurrentDownEvent = MotionEvent.obtain(event)
                cancelLongPress()
                mHandler.sendEmptyMessageDelayed(MSG_LONG_PRESS, mLongPressTimeout)
                mFeedbackHelper.onActionDown()
            }

            MotionEvent.ACTION_MOVE -> {
                touchPoints = event.pointerCount
                if (!isStylusTouch) {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                if (isPointerMoved(event)) {
                    v.parent.requestDisallowInterceptTouchEvent(false)
                    if (mLongPressed) {
                        mHandler.sendEmptyMessage(MSG_DRAG_PRESS)
                    } else {
                        cancelLongPress()
                    }
                }
            }

            MotionEvent.ACTION_UP -> {
                v.parent.requestDisallowInterceptTouchEvent(false)
                mFeedbackHelper.onActionUp()
                cancelLongPress()
                AppLogger.BASIC.d(
                    TAG,
                    "eventTime = ${event.eventTime} downTime = ${event.downTime} isPointerMoved ${
                        isPointerMoved(event)
                    }"
                )
                if ((event.eventTime - event.downTime <= mClickTimeout) && !isPointerMoved(event)) {
                    mHandler.sendMessage(
                        Message.obtain(
                            mHandler, MSG_SINGLE_CLICK, event.x
                        )
                    )
                }
                mCurrentDownEvent = null
            }

            MotionEvent.ACTION_CANCEL -> {
                v.parent.requestDisallowInterceptTouchEvent(false)
                mFeedbackHelper.onActionUp()
                cancelLongPress()
                mCurrentDownEvent = null
            }
        }
        return true
    }

    private fun cancelLongPress() {
        mLongPressed = false
        mHandler.removeMessages(MSG_LONG_PRESS)
        mHandler.removeMessages(MSG_DRAG_PRESS)
    }

    private fun isPointerMoved(e: MotionEvent): Boolean {
        val distance = mCurrentDownEvent?.let {
            val deltaX = (e.x - it.x).toInt()
            val deltaY = (e.y - it.y).toInt()
            deltaX * deltaX + deltaY * deltaY
        } ?: 0
        return distance > mTouchSlopSquare
    }

    private fun perform(type: Int) {
        when (type) {
            MSG_LONG_PRESS -> {
                vibrate(VIBRATE_LONG_PRESS)
                sendAccessibilityEvent(EVENT_LONG_PRESS)
            }

            MSG_DRAG_PRESS -> vibrate(VIBRATE_LONG_PRESS)
            MSG_SINGLE_CLICK -> {
                playSoundEffect(SOUND_CLICK)
                sendAccessibilityEvent(EVENT_CLICK)
            }
        }
    }

    private fun sendAccessibilityEvent(eventType: Int) {
        getView()?.sendAccessibilityEvent(eventType)
    }

    private fun playSoundEffect(soundConstant: Int) {
        getView()?.playSoundEffect(soundConstant)
    }

    private fun vibrate(feedbackConstant: Int) {
        getView()?.performHapticFeedback(feedbackConstant)
    }

    abstract fun onLongPress(v: View)

    /**
     * trigger drag and drop
     * @param v            the view of drag
     * @param dragResult   Whether the startup drag and drop was successful or not
     *
     */
    abstract fun onDragPress(v: View, dragResult: (Boolean) -> Unit)

    abstract fun onClick(v: View, hotZone: Int)
}