/***********************************************************
 * * Copyright (C), 2008-2018, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SettingFragment.java
 * * Description:
 * * Version: 1.0
 * * Date : 2019/08/06 12:30
 * * Author:
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.setting;

import static com.oplus.note.statistic.SmartNoteStatisticsUtils.VALUE_AI_IS_OPEN_FALSE;
import static com.oplus.note.statistic.SmartNoteStatisticsUtils.VALUE_AI_IS_OPEN_TRUE;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import com.oplus.cloudkit.CloudKitGlobalStateManager;
import com.oplus.note.BuildConfig;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import androidx.preference.PreferenceScreen;

import com.coui.appcompat.preference.COUIJumpPreference;
import com.coui.appcompat.preference.COUIPreference;
import com.coui.appcompat.preference.COUIPreferenceCategory;
import com.coui.appcompat.preference.COUISwitchPreference;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.nearme.note.MyApplication;
import com.nearme.note.logic.AccountManagerUtil;
import com.nearme.note.util.ConfigUtils;
import com.nearme.note.util.DeviceInfoUtils;
import com.nearme.note.util.FlexibleWindowUtils;
import com.nearme.note.util.MbaUtils;
import com.nearme.note.util.PrivacyPolicyHelper;
import com.nearme.note.util.RSAProjectHelper;
import com.nearme.note.util.StatisticsUtils;
import com.oplus.cloud.CloudJumpHelperWrapper;
import com.oplus.cloudkit.CloudKitSdkManager;
import com.oplus.cloudkit.util.SyncSwitchStateRepository;
import com.oplus.note.R;
import com.oplus.note.aigc.manager.AIGCSupportManager;
import com.oplus.note.aigc.util.AigcSPUtil;
import com.oplus.note.compat.os.CloudSyncCompact;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.os.OsConfigurations;
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy;
import com.oplus.note.asr.SpeechServiceAgentFactory;
import com.oplus.note.statistic.SmartNoteStatisticsUtils;
import com.oplus.note.utils.PackageInfoUtilKt;

import java.util.List;
import kotlin.Unit;

public class SettingFragment extends BasePreferenceFragment<SettingContract.View, SettingPresenter> implements
        SettingContract.View, Preference.OnPreferenceClickListener {

    public static final String NOTE_SETTING_SYNC_SELECTED = "pref_auto_sync_selected";
    public static final String NOTE_SETTING_APP_VERSION = "pref_auto_sync_version";
    public static final String NOTE_SETTING_NOTIFICATION_CHANNEL = "pref_notification_channel";
    // feedback
    public static final String NOTE_SETTING_HELP_FEEDBACK = "pref_help_feedback";
    public static final String NOTE_SETTING_ABOUT = "pref_about";
    public static final String NOTE_SETTING_PRIVACY = "pref_privacy";
    // setting font
    public static final String NOTE_SETTING_SETTING_FONT = "pref_setting_font";
    public static final String NOTE_SETTING_TITLE = "activity_title";

    public static final String NOTE_SETTING_AUTO_AI_SUMMARY = "pref_auto_ai_summary";
    public static final String NOTE_SETTING_CATEGORY_AUTO_AI = "pref_category_auto_ai";

    public static final String NOTE_SETTING_COLLECT_PERSON_INFORMATION = "pref_collect_person_information";


    public static final String TAG = "NoteSetting";
    public static final String PACKAGE_THEME_SPACE = "com.nearme.themespace";
    public static final String PACKAGE_THEME_STORE = "com.nearme.themestore";
    public static final String PACKAGE_THEME_HEYTAP = "com.heytap.themestore";
    public static final String PACKAGE_THEME_BASE = "com.oplus.themestore";
    private static final String ACTION_FONT_FOR_NOTE = "com.heytap.themestore.action.FONT_FOR_NOTE";
    private static final String ACTION_BASIC_FONT_FOR_NOTE = "com.oplus.themestore.basic.action.FONT_FOR_NOTE";

    private COUIJumpPreference mAutoSyncSelected;
    private COUISwitchPreference mAutoAiSummarySwitchPreference;
    private COUIPreferenceCategory mCategoryAutoAiSwitchCategory;
    private COUIJumpPreference mSettingCollectPersonInformation;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initSettingFontPreference();
        initAISummaryPreference();
        initCollectPersonInformation();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView();
        mPresenter.initData(mActivity.getIntent());

        if ((getContext() != null) && ConfigUtils.isUseCloudKit()) {
            SyncSwitchStateRepository.INSTANCE.querySwitchState(getContext()).observe(getViewLifecycleOwner(), switchState -> {
                AppLogger.BASIC.d(TAG, "switchState:" + switchState);
                if (switchState == CloudKitSdkManager.INSTANCE.getCLOSE_CODE()) {
                    mAutoSyncSelected.setAssignment(getResources().getString(R.string.sync_switch_not_open));
                    mAutoSyncSelected.setSummary("");
                } else {
                    mAutoSyncSelected.setAssignment(getResources().getString(R.string.sync_switch_open));
                    String[] result = SettingPresenter.formatSyncSelectedTxt(mActivity, switchState);
                    mAutoSyncSelected.setSummary(result[SettingPresenter.INDEX_SUMMARY]);
                    mAutoSyncSelected.setAssignment(result[SettingPresenter.INDEX_ASSIGNMENT]);
                }
            });
        }
        mPresenter.setRequestMadeListener();
    }

    @Override
    public void onStart() {
        super.onStart();
        initCollectPersonInformation();
        initSettingFontPreference();
        initSyncModeView();
        initAISummaryPreference();
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        if ((null == preference) || (null == mPresenter)) {
            AppLogger.BASIC.e(TAG, "onPreferenceClick param error ! ");
            return false;
        }
        String key = preference.getKey();
        if (NOTE_SETTING_SYNC_SELECTED.equals(key)) {
            if ((getActivity() != null) && (PackageInfoUtilKt.isPackageDisabled(MbaUtils.PACKAGER_CLOUD, getActivity()))) {
                MbaUtils.INSTANCE.showMbaCloudDialog(getActivity());
            } else {
                if ((getContext() != null) && ConfigUtils.isUseCloudKit()) {
                    if (SyncSwitchStateRepository.INSTANCE.checkIsSupportCloudkitSwitch(getContext())) {
                        SettingsSyncSwitchActivity.Companion.start(mActivity);
                    } else {
                        CloudJumpHelperWrapper.jumpModuleSetting(getContext());
                    }
                } else {
                    CloudJumpHelperWrapper.jumpModuleSetting(getContext());
                }
                StatisticsUtils.setEventSettingAutoSync(mActivity);
            }
        } else if (NOTE_SETTING_SETTING_FONT.equals(key)) {
            // jumpy to theme font setting entry
            mPresenter.openThemeStoreFontSetting(getThemePackageName(MyApplication.getAppContext()));
        } else if (NOTE_SETTING_NOTIFICATION_CHANNEL.equals(key)) {
            mPresenter.openNotificationChannelSettings();
        } else if (NOTE_SETTING_HELP_FEEDBACK.equals(key)) {
            if (PrivacyPolicyHelper.isAgreeUseMedia()) {
                try {
                    mPresenter.openHelpFeedback();
                } catch (Exception ex) {
                    AppLogger.BASIC.d(TAG, "open feedback failed:" + ex);
                }
            } else {
                PrivacyPolicyHelper.showUseMediaDialog(mActivity, isAgree -> {
                    if (isAgree) {
                        PrivacyPolicyHelper.setUseMediaAgreeStatus(mActivity, true);
                        try {
                            mPresenter.openHelpFeedback();
                        } catch (Exception ex) {
                            AppLogger.BASIC.d(TAG, "open feedback failed:" + ex);
                        }
                    }
                    return Unit.INSTANCE;
                });
            }
        } else if (NOTE_SETTING_ABOUT.equals(key)) {
            SettingsAboutActivity.Companion.start(getActivity());
        } else if (NOTE_SETTING_PRIVACY.equals(key)) {
            AppLogger.BASIC.d(TAG, "privacy");
            SettingPrivacyActivity.Companion.start(getActivity());
        } else if (NOTE_SETTING_COLLECT_PERSON_INFORMATION.equals(key)) {
            AppLogger.BASIC.d(TAG, "information");
            SettingsInformationActivity.Companion.start(getActivity());
        } else if (NOTE_SETTING_AUTO_AI_SUMMARY.equals(key)) {
            final boolean[] nowValue = {mAutoAiSummarySwitchPreference.isChecked()};
            AigcSPUtil.setGuidStatus(MyApplication.getAppContext(), nowValue[0]);
            AIGCSupportManager.INSTANCE.checkTextExtractSDK(MyApplication.getAppContext());
            if (nowValue[0] && !PrivacyPolicyHelper.isAgreeAiSummary()) {
                mAutoAiSummarySwitchPreference.setChecked(!nowValue[0]);
                //点击开关，需要弹出AI功能弹窗的场景：1、同意使用便签基本功能；2、撤回对便签【个人的信息保护政策】
                PrivacyPolicyHelper.showAgreeAiSummaryDialog(mActivity, isAgree -> {
                    // 在这里处理用户是否同意额外AI协议的结果
                    if (isAgree) {
                        PrivacyPolicyHelper.setAiSummaryAgreeStatus(MyApplication.getAppContext(), true);
                        AigcSPUtil.setAiSummaryStatus(MyApplication.getAppContext(), true);
                        mAutoAiSummarySwitchPreference.setChecked(true);
                        SmartNoteStatisticsUtils.eventAiAutomaticSummarySwitchStatus(mActivity, VALUE_AI_IS_OPEN_TRUE);
                    }
                    return Unit.INSTANCE;
                });
            } else {
                AigcSPUtil.setAiSummaryStatus(MyApplication.getAppContext(), nowValue[0]);
                if (nowValue[0]) {
                    SmartNoteStatisticsUtils.eventAiAutomaticSummarySwitchStatus(mActivity, VALUE_AI_IS_OPEN_TRUE);
                } else {
                    SmartNoteStatisticsUtils.eventAiAutomaticSummarySwitchStatus(mActivity, VALUE_AI_IS_OPEN_FALSE);
                }
            }
        }
        return true;
    }

    @Override
    protected void initView() {
        PreferenceScreen preferences = getPreferenceScreen();
        mAutoSyncSelected = findPreference(NOTE_SETTING_SYNC_SELECTED);
        if (SpeechServiceAgentFactory.INSTANCE.isBreeno()) {
            if (!DeviceInfoUtils.isAppInstalled(mActivity, MbaUtils.PACKAGER_CLOUD) && mAutoSyncSelected != null) {
                preferences.removePreference(mAutoSyncSelected);
            }
        } else if (SpeechServiceAgentFactory.INSTANCE.isAzure()) {
            if (!CloudSyncCompact.Companion.getInstance().isSupportCloudSync(mActivity) && mAutoSyncSelected != null) {
                preferences.removePreference(mAutoSyncSelected);
            }
        }

        if (!ConfigUtils.isSupportFeedback() && !BuildConfig.isExport) {
            findPreference(NOTE_SETTING_HELP_FEEDBACK).setVisible(false);
        }
        initAppVersionView();
        initNotificationChannelPreference();
        initHelpFeedbackPreference();
        initSettingAbout();
        initSettingPrivacy();

        mActivity.setTitle(getResources().getString(com.oplus.note.baseres.R.string.setting));

        FlexibleWindowUtils.setBackground(mActivity, mView);
    }

    private void initCollectPersonInformation() {
        if (BuildConfig.isExport) {
            return;
        }
        mSettingCollectPersonInformation = findPreference(NOTE_SETTING_COLLECT_PERSON_INFORMATION);
        if (mSettingCollectPersonInformation == null) {
            return;
        }
        if (!OsConfigurations.INSTANCE.isMultiSystem() && CloudSyncCompact.getInstance().isSupportCloudSync(mActivity)
                && !com.oplus.note.BuildConfig.isExport) {
            mSettingCollectPersonInformation.setVisible(true);
            mSettingCollectPersonInformation.setOnPreferenceClickListener(this);
        } else {
            mSettingCollectPersonInformation.setVisible(false);
        }
    }

    @Override
    protected void initActionBar() {
        super.initActionBar();
        boolean isFlexibleActivitySuitable = OplusFlexibleWindowManagerProxy.INSTANCE
                .isFlexibleActivitySuitable(getResources().getConfiguration());
        AppLogger.BASIC.d(TAG, "initActionBar isFlexibleActivitySuitable:" + isFlexibleActivitySuitable);
        if (isFlexibleActivitySuitable) {
            COUIToolbar toolbar = mView.findViewById(R.id.toolbar);
            toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_menu_ic_cancel_normal);
        }
    }

    @Override
    protected int getLayoutResource() {
        if (BuildConfig.isExport) {
            return R.xml.note_settings_export;
        } else {
            return R.xml.note_settings;
        }
    }

    private void initSyncModeView() {
        if (CloudKitGlobalStateManager.deviceOrAccountDisable()) {
            mAutoSyncSelected.setVisible(false);
            return;
        }
        boolean isRSA4AndExport = RSAProjectHelper.Companion.getSIsRSA4AndExport();
        AccountManagerUtil.INSTANCE.checkLogin(getActivity(),getViewLifecycleOwner(), isLogin -> {
            AppLogger.BASIC.d(TAG, "cloud sync switch isRSA4AndExport: " + isRSA4AndExport + " isLogin: " + isLogin);
            if (OsConfigurations.INSTANCE.isGdpr() || (isRSA4AndExport && !isLogin)) {
                mAutoSyncSelected.setVisible(false);
                return null;
            }
            if (SpeechServiceAgentFactory.INSTANCE.isBreeno()) {
                if (OsConfigurations.INSTANCE.isMultiSystem()
                        || !DeviceInfoUtils.isAppInstalled(MyApplication.getAppContext(), MbaUtils.PACKAGER_CLOUD)) {
                    mAutoSyncSelected.setVisible(false);
                    return null;
                }
            } else if (SpeechServiceAgentFactory.INSTANCE.isAzure()) {
                if (OsConfigurations.INSTANCE.isMultiSystem()
                        || !CloudSyncCompact.getInstance().isSupportCloudSync(mActivity)) {
                    mAutoSyncSelected.setVisible(false);
                    return null;
                }
            }
            mAutoSyncSelected.setVisible(true);
            mAutoSyncSelected.setOnPreferenceClickListener(this);
            return null;
        });
    }

    private void initAppVersionView() {
        COUIPreference preference = findPreference(NOTE_SETTING_APP_VERSION);
        if (preference != null) {
            preference.setSummary(MyApplication.getVersion(mActivity, true, true));
        }
    }

    private void initNotificationChannelPreference() {
        COUIJumpPreference preference = findPreference(NOTE_SETTING_NOTIFICATION_CHANNEL);
        if (preference != null) {
            preference.setOnPreferenceClickListener(this);
        }
    }

    private void initSettingAbout() {
        COUIJumpPreference preference = findPreference(NOTE_SETTING_ABOUT);
        if (preference != null) {
            preference.setOnPreferenceClickListener(this);
        }
    }

    private void initSettingPrivacy() {
        COUIJumpPreference preference = findPreference(NOTE_SETTING_PRIVACY);
        if (preference != null) {
            preference.setOnPreferenceClickListener(this);
        }
    }

    private void initHelpFeedbackPreference() {
        if (BuildConfig.isExport) {
            return;
        }
        COUIJumpPreference preference = findPreference(NOTE_SETTING_HELP_FEEDBACK);
        if (preference != null) {
            preference.setOnPreferenceClickListener(this);
        }
    }

    private void initSettingFontPreference() {
        COUIJumpPreference preference = findPreference(NOTE_SETTING_SETTING_FONT);
        if (preference == null) {
            return;
        }
        if (ConfigUtils.isSupportFontSettings()
                && (shouldShowThemeFontEntry(MyApplication.getAppContext(), ACTION_FONT_FOR_NOTE)
                || shouldShowThemeFontEntry(MyApplication.getAppContext(), ACTION_BASIC_FONT_FOR_NOTE))) {
            preference.setVisible(true);
            preference.setOnPreferenceClickListener(this);
        } else {
            preference.setVisible(false);
        }
    }

    private void initAISummaryPreference() {
        boolean isSupport = AIGCSupportManager.INSTANCE.isAiSummarySupport() && AIGCSupportManager.INSTANCE.isTextExtractSupport();
        mCategoryAutoAiSwitchCategory = findPreference(NOTE_SETTING_CATEGORY_AUTO_AI);
        mAutoAiSummarySwitchPreference = findPreference(NOTE_SETTING_AUTO_AI_SUMMARY);
        if (mAutoAiSummarySwitchPreference != null) {
            if (isSupport) {
                mCategoryAutoAiSwitchCategory.setVisible(true);
                mAutoAiSummarySwitchPreference.setVisible(true);
                mAutoAiSummarySwitchPreference.setOnPreferenceClickListener(this);
                if (PrivacyPolicyHelper.isAgreeAiSummary()) { //已同意AI权限，从sp文件拿到开关状态
                    mAutoAiSummarySwitchPreference.setChecked(
                            AigcSPUtil.getAiSummaryStatus(MyApplication.getAppContext()));
                } else { //不同意AI权限，默认关闭
                    mAutoAiSummarySwitchPreference.setChecked(false);
                    mAutoAiSummarySwitchPreference.setChecked(false);
                }
            } else {
                mAutoAiSummarySwitchPreference.setVisible(false);
                mCategoryAutoAiSwitchCategory.setVisible(false);
            }
        }
    }

    private boolean shouldShowThemeFontEntry(Context context, String action) {
        if (null == context) {
            return false;
        }
        String packageName = getThemePackageName(context);
        if (TextUtils.isEmpty(packageName)) {
            return false;
        }
        if (packageName.equals(PACKAGE_THEME_BASE)) {
            return true;
        }
        if (!isIntentExist(context, action)) {
            return false;
        }
        PackageManager packageManager = context.getPackageManager();
        return (null != packageManager) && (null != packageManager.getLaunchIntentForPackage(packageName));
    }

    private String getThemePackageName(Context context) {
        String packageName = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (DeviceInfoUtils.isPackageExist(context, PACKAGE_THEME_HEYTAP)) {
                packageName = PACKAGE_THEME_HEYTAP;
            } else if (DeviceInfoUtils.isPackageExist(context, PACKAGE_THEME_BASE)) {
                packageName = PACKAGE_THEME_BASE;
            }
        } else {
            if (DeviceInfoUtils.isPackageExist(context, PACKAGE_THEME_SPACE)) {
                packageName = PACKAGE_THEME_SPACE;
            } else if (DeviceInfoUtils.isPackageExist(context, PACKAGE_THEME_STORE)) {
                packageName = PACKAGE_THEME_STORE;
            }
        }
        return packageName;
    }

    private boolean isIntentExist(Context context, String action) {
        if (TextUtils.isEmpty(action) || (null == context)) {
            return false;
        }
        Intent intent = new Intent(action);
        String packageName = getThemePackageName(context);
        if (TextUtils.isEmpty(packageName)) {
            return false;
        }
        PackageManager packageManager = context.getPackageManager();
        if (null == packageManager) {
            return false;
        }
        List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent,
                PackageManager.MATCH_DEFAULT_ONLY);
        if ((null == resolveInfos) || (resolveInfos.size() == 0)) {
            return false;
        }
        for (ResolveInfo info : resolveInfos) {
            if ((info != null) && (info.activityInfo != null) && (TextUtils.equals(info.activityInfo.packageName, packageName))) {
                return true;
            }
        }
        return false;
    }


    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        super.onCreatePreferences(savedInstanceState, rootKey);
    }
}
