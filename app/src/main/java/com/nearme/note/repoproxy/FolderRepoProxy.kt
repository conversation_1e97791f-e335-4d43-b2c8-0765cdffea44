/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: FolderRepoProxy.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/01
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.repoproxy

import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.nearme.note.MyApplication
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil
import com.nearme.note.db.daos.FolderDao
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.NoteSearchManagerWrapper
import com.nearme.note.util.refreshCardAll
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.FolderRepo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.utils.NoteStatusProviderUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FolderRepoProxy(private val dao: FolderDao) : FolderRepo {

    companion object {
        private const val TAG = "FolderRepoProxy"
    }

    override fun getViewableFoldersLiveData(): LiveData<List<Folder>> {
        return dao.getViewableFolders().map { folders ->
            runCatching {
                folders.forEach {
                    val sortTime = it.extra?.getSortTime()
                    if (sortTime == null || sortTime <= 0) {
                        it.extra?.setSortTime(it.createTime?.time ?: 0)
                    }
                }
                return@map folders.sortedByDescending { it.extra?.getSortTime() }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "getAllFoldersLiveData error when map by sort.", it)
            }
            return@map folders
        }
    }

    override fun findByGuid(guid: String): Folder? {
        return dao.findByGuid(guid)
    }

    override fun findNotDeletedFolderByName(name: String): List<Folder> {
        return dao.findNotDeletedFolderByName(name)
    }

    override fun insert(folder: Folder) {
        dao.insert(folder)
        AppLogger.BASIC.d(TAG, "insert folder")
        CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
        NoteSearchManagerWrapper.notifyDataChange(true)
    }

    override suspend fun updateFoldersAndNotesForEncryptOrDecrypt(
        folders: List<Folder>,
        shouldEncrypt: Boolean,
        recentDeleteEncryptCallback: ((Boolean) -> Unit)?
    ) {
        kotlin.runCatching {
            val recentDeleteGuid = folders.find { it.guid == FolderInfo.FOLDER_GUID_RECENT_DELETE }
            recentDeleteGuid?.let {
                recentDeleteEncryptCallback?.invoke(shouldEncrypt)
                //存储系统设置需要读取的配置项
                NoteStatusProviderUtil.setRecentDeleteFolderEncrypt(MyApplication.appContext, shouldEncrypt)
                (folders as MutableList).remove(it)
            }
            if (folders.isEmpty()) {
               return
            }
            val result = dao.updateFolders(folders)
            AppLogger.BASIC.d(TAG, "updateFolders result = $result")

            /**
             * 加解密笔记本时，需要将内部笔记改state为STATE_MODIFIED，update_time改为当前时间
             * 备份到云端时，加密笔记本内笔记备份到云端旧表改为DELETE，加密表新增一条数据
             * 备份到云端时，解密笔记本内笔记被分到云端时，加密表数据改为DELETE，旧表数据恢复
             */
            val richNoteDao = AppDatabase.getInstance().richNoteDao()
            val guids = folders.map { it.guid }
            richNoteDao.updateEncrypt(guids, if (shouldEncrypt) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED)
            NoteSearchManagerWrapper.notifyDataChange()
            CloudKitSyncGuidManager.editFolderGuids = guids
            CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
            // 修改笔记本私密属性时，需更新桌面卡片信息
            NoteCardWidgetProvider.instance.postUIToCard(false)
            // 更新桌面插件
            updateWidgets(guids)
            refreshCardAll(MyApplication.appContext)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "update error.", it)
        }
    }

    private fun updateWidgets(guids: List<String>) {
        val richNoteDao = AppDatabase.getInstance().richNoteDao()
        val localIds = richNoteDao.findByFolderGuids(guids).map {
            it.localId
        }.toSet()
        WidgetUtils.sendNoteDataChangedBroadcast(MyApplication.appContext, localIds)
    }

    override suspend fun update(folders: List<Folder>) = withContext(Dispatchers.IO) {
        val result = dao.updateFolders(folders)
        AppLogger.BASIC.d(TAG, "update folder $result")
        CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
        NoteSearchManagerWrapper.notifyDataChange(true)
        return@withContext result
    }

    override suspend fun deleteFolders(folders: MutableList<Folder>) = withContext(Dispatchers.IO) {
        folders.forEach {
            DataStatisticsHelper.folderUserOps(TAG, "01030300", it.guid, it.name)
        }

        val result = FolderUtil.deleteFoldersSyncForRichNote(
            MyApplication.appContext,
            folders.map { it.guid },
            true,
            true,
            false
        )
        AppLogger.BASIC.d(TAG, "deleteFolders result=$result")
        CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
    }
}