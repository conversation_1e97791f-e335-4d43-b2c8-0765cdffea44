/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: RepoProxyInitializer.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/01
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.repoproxy

import android.content.Context
import androidx.startup.Initializer
import com.nearme.note.db.AppDatabase
import com.oplus.note.aigraffiti.AIGraffitiProxyFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.NoteRepoFactory
import com.oplus.note.repo.todo.TodoRepoFactory

class RepoProxyInitializer : Initializer<Boolean> {

    companion object {
        private const val TAG = "Notes.RepoProxyInitializer"
        private const val DEBUG_PROP_AI_GRAFFITI = "debug.oplus.smardsidebar.aigraffiti"
    }

    override fun create(context: Context): Boolean {
        AppLogger.BASIC.d(TAG, "register RepoProxyInitializer")
        TodoRepoFactory.register(TodoRepoProxy())
        NoteRepoFactory.register(NoteRepoProxy())
        NoteRepoFactory.register(FolderRepoProxy(AppDatabase.getInstance().foldersDao()))
        // ai 涂鸦用户协议监听
        AIGraffitiProxyFactory.register(AIGraffitiPrivacyProxy())
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        // No dependencies on other libraries.
        return mutableListOf()
    }
}