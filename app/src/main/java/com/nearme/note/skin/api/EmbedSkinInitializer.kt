/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: EmbedSkinInitializer.kt
 * * Description: EmbedSkinInitializer
 * * Version: 1.0
 * * Date: 2020/06/09
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.skin.api

import android.util.ArrayMap
import com.nearme.note.MyApplication
import com.nearme.note.skin.SkinData.*
import com.nearme.note.skin.bean.Skin
import com.nearme.note.skin.bean.SkinSummary
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.BuildConfig

object EmbedSkinInitializer {

    fun initiateSkinSummaries(embedSkinSummaries: ArrayList<SkinSummary>) {

        if (isAddManualSkin) {
            embedSkinSummaries.add(SkinSummary(id = SKIN_CLASSIFY, thumbnail = "", detail = COLOR_TITLE))
        }
        embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_WHITE, thumbnail = COLOR_SKIN_WHITE_FOREGROUND, detail = COLOR_SKIN_WHITE))
        if (!(BuildConfig.isExport || UiHelper.isDevicePad())) {
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_YELLOW, thumbnail = COLOR_SKIN_YELLOW_FOREGROUND, detail = COLOR_SKIN_YELLOW))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_CYAN, thumbnail = COLOR_SKIN_CYAN_FOREGROUND, detail = COLOR_SKIN_CYAN))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_BLUE, thumbnail = COLOR_SKIN_BLUE_FOREGROUND, detail = COLOR_SKIN_BLUE))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_GREEN, thumbnail = COLOR_SKIN_GREEN_FOREGROUND, detail = COLOR_SKIN_GREEN))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_RED, thumbnail = COLOR_SKIN_RED_FOREGROUND, detail = COLOR_SKIN_RED))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_GREY, thumbnail = COLOR_SKIN_GREY_FOREGROUND, detail = COLOR_SKIN_GREY))
        }

        if (isAddManualSkin) {
            embedSkinSummaries.add(SkinSummary(id = SKIN_CLASSIFY, thumbnail = "", detail = GRID_TITLE))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_HORIZON_LINE,
                    thumbnail = COLOR_SKIN_HORIZON_LINE_FOREGROUND, detail = COLOR_SKIN_HORIZON_LINE))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_GRID_LINE,
                    thumbnail = COLOR_SKIN_GRID_LINE_FOREGROUND, detail = COLOR_SKIN_GRID_LINE))
            embedSkinSummaries.add(SkinSummary(id = COLOR_SKIN_GRID_DOT, thumbnail = COLOR_SKIN_GRID_DOT_FOREGROUND, detail = COLOR_SKIN_GRID_DOT))
            if (!(BuildConfig.isExport || UiHelper.isDevicePad())) {
                embedSkinSummaries.add(SkinSummary(id = SKIN_CLASSIFY, thumbnail = "", detail = CUSTOM_TITLE))
            }
        }

        if (!(BuildConfig.isExport || UiHelper.isDevicePad())) {
            val packageName = MyApplication.application.packageName
            embedSkinSummaries.add(SkinSummary(
                    id = IMG_SKIN_1,
                    thumbnail = "android.resource://$packageName/drawable/img_skin_1_thumbnail",
                    detail = IMG_SKIN_1
            ))
            embedSkinSummaries.add(SkinSummary(
                    id = IMG_SKIN_2,
                    thumbnail = "android.resource://$packageName/drawable/img_skin_2_thumbnail",
                    detail = IMG_SKIN_2
            ))
            embedSkinSummaries.add(SkinSummary(
                    id = IMG_SKIN_3,
                    thumbnail = "android.resource://$packageName/drawable/img_skin_3_thumbnail",
                    detail = IMG_SKIN_3
            ))
            embedSkinSummaries.add(SkinSummary(
                    id = IMG_SKIN_4,
                    thumbnail = "android.resource://$packageName/drawable/img_skin_4_thumbnail",
                    detail = IMG_SKIN_4
            ))
        }
    }
        @Suppress("LongMethod")
    fun initiateSkins(embedSkins: ArrayMap<String, Skin>) {
        embedSkins[COLOR_SKIN_WHITE] = Skin(
                editPage = Skin.EditPage(
                        background = Skin.EditPage.Background(
                                contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#FFFFFFFF")
                        ),
                        backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFFAFAFA")
                )
        )

        embedSkins[COLOR_SKIN_BLACK] = Skin(
                card = Skin.Card(
                        bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#1B1B1B"),
                        titleColor = "#FFFFFFFF",
                        contentColor = "#8CFFFFFF",
                        timeColor = "#8CFFFFFF"
                ),
                editPage = Skin.EditPage(
                        background = Skin.EditPage.Background(
                                contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#FF000000")
                        ),
                        title = Skin.EditPage.Title(
                                textColor = "#FFFFFFFF"
                        ),
                        content = Skin.EditPage.Content(
                                textColor = "#FFFFFFFF"
                        ),
                        checkbox = Skin.EditPage.Checkbox(
                                check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#80FFFFFF"),
                                uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#80FFFFFF")
                        ),
                        timeColor = "#4DFFFFFF",
                        backcloth = Skin.EditPage.BackCloth(backClothColor = "#FF2E2E2E")
                ),
                sharePage = Skin.SharePage(
                        background = Skin.SharePage.Background(
                                contentBg = Skin.SharePage.Background.ContentBg("#FF222222", 0, Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#FF222222"))
                )
        )

        if (!(BuildConfig.isExport || UiHelper.isDevicePad())) {
            embedSkins[COLOR_SKIN_YELLOW] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#fbf7e8"),
                            titleColor = "#5f4a33",
                            contentColor = "#cc5f4a33",
                            timeColor = "#665f4a33"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#fffef7e2")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#96826c"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#96826c"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#8096826c"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#8096826c")
                            ),
                            timeColor = "#6696826c",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFEFE8D4")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#fffef7e2", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#fffef7e2"))
                    )
            )


            embedSkins[COLOR_SKIN_CYAN] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#edf5ee"),
                            titleColor = "#515d54",
                            contentColor = "#cc515d54",
                            timeColor = "#66515d54"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeff7f0")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#747d76"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#747d76"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#80747d76"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#80747d76")
                            ),
                            timeColor = "#66747d76",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFE1E8E2")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#ffeff7f0", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeff7f0"))
                    )
            )

            embedSkins[COLOR_SKIN_BLUE] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#eef3f3"),
                            titleColor = "#3b5050",
                            contentColor = "#cc3b5050",
                            timeColor = "#663b5050"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeaf4f3")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#607474"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#607474"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#80607474"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#80607474")
                            ),
                            timeColor = "#66607474",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFDCE5E4")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#ffeaf4f3", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeaf4f3"))
                    )
            )

            embedSkins[COLOR_SKIN_GREEN] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#ebf2f6"),
                            titleColor = "#4e5960",
                            contentColor = "#cc4e5960",
                            timeColor = "#664e5960"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeaf3f8")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#5a656c"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#5a656c"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#805a656c"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#805a656c")
                            ),
                            timeColor = "#665a656c",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFDCE4E9")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#ffeaf3f8", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#ffeaf3f8"))
                    )
            )

            embedSkins[COLOR_SKIN_RED] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#f4efea"),
                            titleColor = "#795c4c",
                            contentColor = "#cc795c4c",
                            timeColor = "#66795c4c"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#fff8f1e9")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#9f7660"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#9f7660"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#809f7660"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#809f7660")
                            ),
                            timeColor = "#669f7660",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFE9E3DB")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#fff8f1e9", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#fff8f1e9"))
                    )
            )

            embedSkins[COLOR_SKIN_GREY] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PURE_COLOR, "#f2f2f2"),
                            titleColor = "#474747",
                            contentColor = "#cc474747",
                            timeColor = "#66474747"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#fff4f4f4")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#5f5f5f"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#5f5f5f"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_COLOR, "#805f5f5f"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_COLOR, "#805f5f5f")
                            ),
                            timeColor = "#665f5f5f",
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFE5E5E5")
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg("#fff4f4f4", 0,
                                            Skin.SharePage.Background.ContentBg.TYPE_PURE_COLOR, "#fff4f4f4"))
                    )
            )
        }
        val packageName = MyApplication.application.packageName
        if (isAddManualSkin) {
            embedSkins[COLOR_SKIN_HORIZON_LINE] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE_TILE, "android.resource://$packageName/drawable/card_bg_horizontal"),
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#FFFAFAFA")
                            ),
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFEBEBEB")
                    )
            )
            embedSkins[COLOR_SKIN_GRID_LINE] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE_TILE, "android.resource://$packageName/drawable/card_bg_grid"),
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#FFFAFAFA")
                            ),
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFEBEBEB")
                    )
            )
            embedSkins[COLOR_SKIN_GRID_DOT] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE_TILE, "android.resource://$packageName/drawable/card_bg_dot"),
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PURE_COLOR, "#FFFAFAFA")
                            ),
                            backcloth = Skin.EditPage.BackCloth(backClothColor = "#FFEBEBEB")
                    )
            )
        }

        // TODO optimize loading local resource
        if (!(BuildConfig.isExport || UiHelper.isDevicePad())) {
            embedSkins[IMG_SKIN_1] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE, "android.resource://$packageName/drawable/img_skin_1_card_bg"),
                            titleColor = "#FFC5AD57",
                            contentColor = "#FF4D4836",
                            timeColor = "#8C000000"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_1_edit_content_bg")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#FFD8C88D",
                                    left = "50",
                                    top = "23",
                                    right = "50",
                                    bottom = "0",
                                    textSize = "26",
                                    lineGap = "0"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#FF4D4836",
                                    left = "50",
                                    top = "15",
                                    right = "50",
                                    bottom = "90",
                                    textSize = "15",
                                    lineGap = "6"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_1_edit_checkbox_on"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_1_edit_checkbox_off")
                            )
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg(
                                            type = Skin.SharePage.Background.ContentBg.TYPE_PICTURE,
                                            value = "android.resource://$packageName/drawable/img_skin_1_share_content_bg"
                                    )
                            )
                    )
            )

            embedSkins[IMG_SKIN_2] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE, "android.resource://$packageName/drawable/img_skin_2_card_bg"),
                            titleColor = TEXT_COLOR,
                            contentColor = "#CC000000",
                            timeColor = "#8C000000"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_2_edit_content_bg")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = TEXT_COLOR,
                                    left = "40",
                                    top = "8",
                                    right = "40",
                                    bottom = "0",
                                    textSize = "26",
                                    lineGap = "0"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = TEXT_COLOR,
                                    left = "40",
                                    top = "20",
                                    right = "40",
                                    bottom = "90",
                                    textSize = "16",
                                    lineGap = "9"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_2_edit_checkbox_on"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_2_edit_checkbox_off")
                            )
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg(
                                            type = Skin.SharePage.Background.ContentBg.TYPE_PICTURE,
                                            value = "android.resource://$packageName/drawable/img_skin_2_share_content_bg"
                                    )
                            )
                    )
            )

            embedSkins[IMG_SKIN_3] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE, "android.resource://$packageName/drawable/img_skin_3_card_bg"),
                            titleColor = "#FF7D585A",
                            contentColor = "#CC7D585A",
                            timeColor = "#4D7D585A"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_3_edit_content_bg")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#FFD88C9B",
                                    left = "32",
                                    top = "0",
                                    right = "32",
                                    bottom = "16",
                                    textSize = "26",
                                    lineGap = "4"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#FF6F5357",
                                    left = "32",
                                    top = "0",
                                    right = "32",
                                    bottom = "90",
                                    textSize = "15",
                                    lineGap = "10"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_3_edit_checkbox_on"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_3_edit_checkbox_off")
                            )
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg(
                                            type = Skin.SharePage.Background.ContentBg.TYPE_PICTURE,
                                            value = "android.resource://$packageName/drawable/img_skin_3_share_content_bg"
                                    )
                            )
                    )
            )

            embedSkins[IMG_SKIN_4] = Skin(
                    card = Skin.Card(
                            bg = Skin.Card.Bg(Skin.Card.Bg.TYPE_PICTURE, "android.resource://$packageName/drawable/img_skin_4_card_bg"),
                            titleColor = "#FF766555",
                            contentColor = "#CC766555",
                            timeColor = "#4D766555"
                    ),
                    editPage = Skin.EditPage(
                            background = Skin.EditPage.Background(
                                    contentBg = Skin.EditPage.Background.ContentBg(Skin.EditPage.Background.ContentBg.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_4_edit_content_bg")
                            ),
                            title = Skin.EditPage.Title(
                                    textColor = "#FF6C5346",
                                    left = "44",
                                    top = "18",
                                    right = "44",
                                    bottom = "0",
                                    textSize = "26",
                                    lineGap = "0",
                                    align = "center"
                            ),
                            content = Skin.EditPage.Content(
                                    textColor = "#FF6C5346",
                                    left = "44",
                                    top = "20",
                                    right = "44",
                                    bottom = "90",
                                    textSize = "15",
                                    lineGap = "6"
                            ),
                            checkbox = Skin.EditPage.Checkbox(
                                    check = Skin.EditPage.Checkbox.Check(Skin.EditPage.Checkbox.Check.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_4_edit_checkbox_on"),
                                    uncheck = Skin.EditPage.Checkbox.Uncheck(Skin.EditPage.Checkbox.Uncheck.TYPE_PICTURE,
                                            "android.resource://$packageName/drawable/img_skin_4_edit_checkbox_off")
                            )
                    ),
                    sharePage = Skin.SharePage(
                            background = Skin.SharePage.Background(
                                    contentBg = Skin.SharePage.Background.ContentBg(
                                            type = Skin.SharePage.Background.ContentBg.TYPE_PICTURE,
                                            value = "android.resource://$packageName/drawable/img_skin_4_share_content_bg"
                                    )
                            )
                    )
            )
        }
    }
}