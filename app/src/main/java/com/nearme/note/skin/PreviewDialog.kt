package com.nearme.note.skin

import android.app.AlertDialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.bumptech.glide.request.target.DrawableImageViewTarget
import com.bumptech.glide.request.transition.Transition
import com.nearme.note.util.glideWithAvailable
import com.oplus.note.logger.AppLogger
import com.oplus.note.R

open class PreviewDialog constructor(context: Context, previewUrl: String, skinBoard: View) : AlertDialog(context) {
    companion object {
        const val TAG = "PreviewDialog"
    }

    private var mContext: Context = context
    private var mPreviewUrl: String = previewUrl

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.decorView?.setPadding(0, 0, 0, 0)
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        hideSystemUI()
        val previewLayout: View = layoutInflater.inflate(R.layout.dialog_skin_preview, null)
        val previewImageView: ImageView = previewLayout.findViewById(R.id.previewImage)
        val loadingView: View = previewLayout.findViewById(R.id.loading)
        val loadErrorView: View = previewLayout.findViewById(R.id.load_error)
        val drawableRequestBuilder = mContext.glideWithAvailable()?.load(mPreviewUrl)
        drawableRequestBuilder?.into(object : DrawableImageViewTarget(previewImageView) {
            override fun onLoadFailed(errorDrawable: Drawable?) {
                super.onLoadFailed(errorDrawable)
                AppLogger.BASIC.d(TAG, "onLoadFailed")

                loadingView.visibility = View.GONE
                loadErrorView.visibility = View.VISIBLE
            }

            override fun onLoadStarted(placeholder: Drawable?) {
                super.onLoadStarted(placeholder)
                AppLogger.BASIC.d(TAG, "onLoadStarted")
                loadingView.visibility = View.VISIBLE
                previewImageView.visibility = View.GONE
                loadErrorView.visibility = View.GONE
            }

            override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                super.onResourceReady(resource, transition)
                AppLogger.BASIC.d(TAG, "onResourceReady")
                loadingView.visibility = View.GONE
                previewImageView.visibility = View.VISIBLE
            }
        })
        previewLayout.setOnClickListener {
            dismiss()
        }

        setContentView(previewLayout)
        window?.setBackgroundDrawable(ContextCompat.getDrawable(mContext, R.drawable.skin_preview_background))
    }

    private fun hideSystemUI() {
        val decorView = window!!.decorView
        decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE // Set the content to appear under the system bars so that the
                // content doesn't resize when the system bars hide and show.
                or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN // Hide the nav bar and status bar
                or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or
                View.SYSTEM_UI_FLAG_FULLSCREEN)
        window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window?.setNavigationBarColor(Color.TRANSPARENT);
    }
}