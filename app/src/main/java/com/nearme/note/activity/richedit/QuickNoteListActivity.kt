package com.nearme.note.activity.richedit

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.nearme.note.BaseActivity
import com.nearme.note.db.NotesProvider
import com.nearme.note.util.IntentParamsUtil
import com.oplus.note.R

open class QuickNoteListActivity : BaseActivity() {

    companion object {
        fun createIntent(context: Context, name: String, folderGuid: String?): Intent {
            return Intent(context, QuickNoteListActivity::class.java).apply {
                putExtra(NotesProvider.COL_NOTE_FOLDER_GUID, folderGuid)
                putExtra(NotesProvider.COL_FOLDER_NAME, name)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_quick_list)
        addNoteListFragment()
    }

    private fun addNoteListFragment() {
        if(supportFragmentManager.findFragmentByTag(QuickNoteListFragment.TAG)==null){
            var fragment = QuickNoteListFragment.newInstance(
                IntentParamsUtil.getStringExtra(intent, NotesProvider.COL_FOLDER_NAME, null),
                IntentParamsUtil.getStringExtra(intent, NotesProvider.COL_NOTE_FOLDER_GUID, null)
            )
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, fragment, QuickNoteListFragment.TAG)
                .commitNow()
        }
    }

}