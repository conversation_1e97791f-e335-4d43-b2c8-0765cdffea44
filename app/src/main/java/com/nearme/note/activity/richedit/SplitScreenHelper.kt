/****************************************************************
 ** Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - SplitScreenHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/9/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/9/1     1.0            add file
 ****************************************************************/
package com.nearme.note.activity.richedit

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.component.responsiveui.ResponsiveUIFeature
import com.coui.component.responsiveui.status.FoldingState
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.guide.GuideTipManager
import com.nearme.note.util.AccessibilityUtils
import com.oplus.note.osdk.proxy.ActivityMultiWindowAllowanceProxy
import com.oplus.note.osdk.proxy.ActivityMultiWindowAllowanceObserverProxy
import com.oplus.note.osdk.proxy.MultiWindowTriggerProxy
import com.nearme.note.util.SplitScreenUtil
import com.nearme.note.util.StatisticsUtils
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

interface SplitScreenListener {
    fun onClickSplitScreen(multiWindowAllowance: ActivityMultiWindowAllowanceProxy?, multiWindowTrigger: MultiWindowTriggerProxy?)
}

class SplitScreenHelper(private val mSplitScreenListener: SplitScreenListener) {

    companion object {
        private const val TAG = "SplitScreenHelper"

        const val ALPHA_ENABLE = 255
        const val ALPHA_DISABLE = 77
        private const val DELAY = 500L
        private const val ICON_MARGIN_START_SPLIT = 41
        const val ICON_MARGIN_START_NOT_SPLIT = 28
    }

    private var mMultiWindowTrigger: MultiWindowTriggerProxy? = null
    private lateinit var mAllowanceObserver: ActivityMultiWindowAllowanceObserverProxy
    private var multiWindowAllowance: ActivityMultiWindowAllowanceProxy? = null
    private var splitScreenImageView: ImageView? = null
    private var noEditableToast: Toast? = null
    //分屏状态，页面被禁用时为true
    var mDisableWhenSplitScreen = false
    private var showSplitIcon = false
    private var toolbar: COUIToolbar? = null
    private var clickIconListener: (() -> Unit)? = null
    private var editFragment: WVNoteViewEditFragment? = null

    fun onCreate(activity: Activity) {
        noEditableToast = Toast.makeText(activity, "", Toast.LENGTH_SHORT)
        if (activity is QuickNoteViewRichEditActivity) {
            //小窗速记页面不显示分屏按钮，不用加载相关逻辑
            return
        }
        try {
            mMultiWindowTrigger = MultiWindowTriggerProxy()
            val supportSplitScreen = mMultiWindowTrigger!!.isDeviceSupport(activity)
            AppLogger.BASIC.d(TAG, "supportSplitScreen $supportSplitScreen")
            if (!supportSplitScreen) {
                mMultiWindowTrigger = null
            }
        } catch (e: Throwable) {
            AppLogger.BASIC.e(TAG, "split screen init error ${e.message}")
            mMultiWindowTrigger = null
        }
        if (mMultiWindowTrigger != null) {
            mAllowanceObserver = AllowanceObserver(activity, this)
        }
        GuideTipManager.setTipTimes(GuideTipManager.KEY_SPLIT_SCREEN_TIMES_TIP)
    }

    private class AllowanceObserver(activity: Activity, helper: SplitScreenHelper) : ActivityMultiWindowAllowanceObserverProxy() {
        private val weakRef = WeakReference(activity)
        private val weakRefHelper = WeakReference(helper)
        override fun onMultiWindowAllowanceChanged(allowance: ActivityMultiWindowAllowanceProxy) {
            AppLogger.BASIC.d(TAG, "allowance allowSelfSplitToSplitScreen: ${allowance.allowSelfSplitToSplitScreen}" +
                    " allowSwitchToSplitScreen：${allowance.allowSwitchToSplitScreen}")
            weakRefHelper.get()?.multiWindowAllowance = allowance
            weakRef.get()?.runOnUiThread {
                val visible = allowance.allowSelfSplitToSplitScreen || allowance.allowSwitchToSplitScreen
                if (!visible) {
                    GuideTipManager.dismissSplitTipsIfSplitScreenGone()
                }
                val splitIconMarginStart = if (allowance.allowSelfSplitToSplitScreen) {
                    ICON_MARGIN_START_SPLIT
                } else {
                    ICON_MARGIN_START_NOT_SPLIT
                }
                weakRefHelper.get()?.showSplitScreenIcon(visible, splitIconMarginStart)
            }
        }
    }

    fun onStart(activity: Activity, scope: CoroutineScope) {
        mMultiWindowTrigger?.apply {
            scope.launch(Dispatchers.IO) {
                AppLogger.BASIC.d(TAG, "registerActivityMultiWindowAllowanceObserver")
                registerActivityMultiWindowAllowanceObserver(activity, mAllowanceObserver)
            }
        }
    }

    fun onStop(activity: Activity, scope: CoroutineScope) {
        mMultiWindowTrigger?.apply {
            scope.launch(Dispatchers.IO) {
                AppLogger.BASIC.d(TAG, "unregisterActivityMultiWindowAllowanceObserver")
                unregisterActivityMultiWindowAllowanceObserver(activity, mAllowanceObserver)
            }
        }
    }

    fun reRegisterMultiWindowAllowanceObserver(activity: Activity, scope: CoroutineScope) {
        mMultiWindowTrigger?.apply {
            scope.launch(Dispatchers.IO) {
                AppLogger.BASIC.d(TAG, "re-RegisterMultiWindowAllowanceObserver")
                unregisterActivityMultiWindowAllowanceObserver(activity, mAllowanceObserver)
                registerActivityMultiWindowAllowanceObserver(activity, mAllowanceObserver)
            }
        }
    }

    fun onDestroy() {
        editFragment = null
    }

    fun initToolBarLogo(fragment: WVNoteViewEditFragment?, toolbar: COUIToolbar?, listener: () -> Unit) {
        editFragment = fragment
        this.toolbar = toolbar
        this.clickIconListener = listener
        this.toolbar?.let {
            it.postDelayed({ showSplitScreenTips(it.context) }, DELAY)
        }
    }

    fun onClickSplitScreen() {
        mSplitScreenListener.onClickSplitScreen(multiWindowAllowance, mMultiWindowTrigger)
    }

    fun showSplitScreenTips(context: Context) {
        //无障碍模式，tips弹窗将影响无障碍事件传递，造成盲文键盘不播报语音，故无障碍模式不显示tips
        if (!GuideTipManager.getTipsIsShowed(GuideTipManager.KEY_SPLIT_SCREEN_TIP)
            && !OplusFlexibleWindowManagerProxy.isInFreeFormMode(context as Activity)
            && !AccessibilityUtils.isTalkBackAccessibility(context)
            && showSplitIcon
        ) {
            GuideTipManager.splitScreenShowTip(splitScreenImageView)
        }
    }

    fun updateIconInDarkMode(color: Int) {
        splitScreenImageView?.takeIf { showSplitIcon }?.also {
            AppCompatResources.getDrawable(it.context, R.drawable.ic_split_screen)?.apply {
                setTint(color)
                it.setImageDrawable(this)
            }
        }
    }

    fun showNotEditToast() {
        noEditableToast?.setText(R.string.toast_cannot_edit_in_split_screen)
        noEditableToast?.show()
    }

    fun showSplitScreenIcon(show: Boolean, splitIconMarginStart: Int) {
        showSplitIcon = show
        if (show) {
            toolbar?.also {
                AppCompatResources.getDrawable(it.context, R.drawable.ic_split_screen)?.apply {
                    editFragment?.let { fragment ->
                        setTint(if (fragment.mIsTextDark) Color.BLACK else Color.WHITE)
                    }
                    it.logo = this
                }
                it.logoDescription = it.context.getString(R.string.tips_display_split_screen)
                if (splitScreenImageView == null) {
                    splitScreenImageView = SplitScreenUtil.getSplitScreenIcon(it)?.apply {
                        val params = layoutParams as ViewGroup.MarginLayoutParams
                        params.marginStart = splitIconMarginStart
                        layoutParams = params
                        setBackgroundResource(com.support.appcompat.R.drawable.coui_toolbar_menu_bg)
                        setOnClickListener {
                            GuideTipManager.setTipsShowed(GuideTipManager.KEY_SPLIT_SCREEN_TIP)
                            clickIconListener?.invoke()
                            StatisticsUtils.setEventClickSplitScreen(MyApplication.appContext)
                        }
                    }
                }
                val isSpeechMode = editFragment?.isShowSpeechDialog ?: false
                updateSplitIconEnable(!isSpeechMode)
                if (editFragment?.webSearchOperationController?.isSearchMode() == true) {
                    splitScreenImageView?.visibility = View.GONE
                }
            }
        } else {
            toolbar?.logo = null
        }
    }

    fun updateSplitIconEnable(enable: Boolean) {
        /**
         * 在AI生成过程中，禁用分屏按钮
         */
        splitScreenImageView?.isEnabled = enable
        splitScreenImageView?.imageAlpha = if (enable) {
            ALPHA_ENABLE
        } else {
            ALPHA_DISABLE
        }
    }

    /**
     * @return true 没有在单应用分屏状态  false 在单应用分屏状态
     */
    fun isNotInSingleAPPSplit(activity: Activity): Boolean {
        val state = ResponsiveUIFeature.getFoldingState(activity)
        if (state == FoldingState.FOLD) {
            AppLogger.BASIC.d(TAG, "isNotInSingleAPPSplit# FoldingState=FOLD")
            return true
        }
        AppLogger.BASIC.d(
            TAG,
            "isNotInSingleAPPSplit# allowSwitchToSplitScreen: ${multiWindowAllowance?.allowSwitchToSplitScreen}"
        )
        return multiWindowAllowance == null || multiWindowAllowance?.allowSwitchToSplitScreen == true
    }
}