/***********************************************************
 * * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.view.Surface;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.coui.appcompat.panel.COUIBottomSheetDialogFragment;
import com.coui.appcompat.panel.COUIPanelFragment;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.nearme.note.DialogFactory.DialogOnClickListener;
import com.nearme.note.setting.Setting;
import com.nearme.note.util.StatisticsUtils;
import com.nearme.note.util.WindowInsetsUtil;
import com.nearme.note.view.NoteCOUIBottomSheetDialogFragment;
import com.nearme.note.view.helper.UiHelper;
import com.oplus.note.R;
import com.oplus.note.edgeToEdge.EdgeToEdgeActivity;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.os.ResponsiveUiHelper;
import com.oplus.note.osdk.proxy.FlexibleWindowManagerProxy;
import com.oplus.note.osdk.proxy.OplusBuildProxy;

@SuppressLint("Registered")
public class BaseActivity extends EdgeToEdgeActivity implements DialogOnClickListener, View.OnLayoutChangeListener {
    private static final String TAG = "BaseActivity";

    public boolean mInMultiWindowBottom = false;
    protected DialogFactory mDialogFactory;

    private final int[] mLocationInScreen = new int[2];
    private boolean mRegistFlag = false;
    private boolean mEnableStatusBarReceiver = true;

    private BroadcastReceiver mStatubarClickReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context paramContext, Intent paramIntent) {
            backtoTop();
        }
    };

    public void setScreenOrientation() {
        ResponsiveUiHelper.setOrientation(this);
    }

    @Override
    @SuppressLint("NewApi")
    protected void onCreate(Bundle savedInstanceState) {
        setScreenOrientation();
        super.onCreate(savedInstanceState);

        getWindow().getDecorView().addOnLayoutChangeListener(this);
        int layoutResource = getLayoutResource();
        if (layoutResource != -1) {
            setContentView(getLayoutResource());
        }
        COUIThemeOverlay.getInstance().applyThemeOverlays(this);
        initView();
        initActionBar();
        initWindowStyle();
        UiHelper.initStatics(this);
        StatisticsUtils.onError(this);
        Setting.getInstance();
        mDialogFactory = new DialogFactory(this, this);
    }

    protected void initActionBar() {
    }

    protected void initView() {
    }

    protected int getLayoutResource() {
        return -1;
    }

    @Override
    public boolean isInMultiWindowMode() {
        if (OplusBuildProxy.INSTANCE.isAboveOS150()) {
            try {
                return FlexibleWindowManagerProxy.getFlexibleWindowState(this)
                        == FlexibleWindowManagerProxy.FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE;
            } catch (Exception e) {
                AppLogger.BASIC.e(TAG, "isInMultiWindowMode error:${it.message}");
                return false;
            }
        } else {
            return super.isInMultiWindowMode();
        }
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        if (isInMultiWindowMode) {
            StatisticsUtils.setEventMultiWindow();
        }
    }

    private boolean isAtBottomInMultiWindow() {
        boolean isInMultiWindowMode = isInMultiWindowMode();
        if (isInMultiWindowMode) {
            getWindow().getDecorView().getLocationOnScreen(mLocationInScreen);
            return mLocationInScreen[1] > WindowInsetsUtil.getStatusBarHeight(this);
        } else {
            return false;
        }
    }

    public boolean isAtPrimaryHorizontalInMultiWindow() {
        boolean isInMultiWindowMode = isInMultiWindowMode();
        if (isInMultiWindowMode) {
            getWindow().getDecorView().getLocationOnScreen(mLocationInScreen);
            int rotation = getWindowManager().getDefaultDisplay().getRotation();

            if (rotation == Surface.ROTATION_90) {
                return mLocationInScreen[0] == 0 && mLocationInScreen[1] == 0;
            } else if (rotation == Surface.ROTATION_270) {
                return mLocationInScreen[0] > WindowInsetsUtil.getStatusBarHeight(this) && mLocationInScreen[1] == 0;
            }
        }
        return false;
    }

    public boolean isHorizontalInMultiWindow() {
        boolean isInMultiWindowMode = isInMultiWindowMode();
        if (isInMultiWindowMode) {
            getWindow().getDecorView().getLocationOnScreen(mLocationInScreen);
            int rotation = getWindowManager().getDefaultDisplay().getRotation();

            if (rotation == Surface.ROTATION_90 || rotation == Surface.ROTATION_270) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
        if (mInMultiWindowBottom != isAtBottomInMultiWindow()) {
            notifyInMultiWindowBottom(mInMultiWindowBottom = !mInMultiWindowBottom);
        }
        notifyInMultiWindowPrimaryHorizontal(isAtPrimaryHorizontalInMultiWindow());
    }

    public void notifyInMultiWindowBottom(boolean inMultiWindowBottom) {

    }

    public void notifyInMultiWindowPrimaryHorizontal(boolean isPrimaryHorizontal) {

    }

    public void initWindowStyle() {

    }

    @Override
    protected void onResume() {
        registerStatubarReceiver();
        super.onResume();
        StatisticsUtils.onResume(this);
    }

    @Override
    protected void onPause() {
        unregisterStatubarReceiver();
        super.onPause();
        StatisticsUtils.onPause(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        getWindow().getDecorView().removeOnLayoutChangeListener(this);
        if (mDialogFactory != null) {
            mDialogFactory.onDestory();
            mDialogFactory = null;
        }
    }

    public DialogFactory getDialogFactory() {
        return mDialogFactory;
    }

    protected void backtoTop() {
        //subclass implementation
    }

    @SuppressLint("WrongConstant")
    private void registerStatubarReceiver() {
        if (!mEnableStatusBarReceiver) {
            return;
        }
        if (!mRegistFlag) {
            try {
                IntentFilter intentFilter = new IntentFilter();
                if (OplusBuildProxy.INSTANCE.isAboveOS113()) {
                    intentFilter.addAction("com.oplus.clicktop");
                } else {
                    intentFilter.addAction("com.color.clicktop");
                }
                ContextCompat.registerReceiver(this, mStatubarClickReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED);
            } catch (Exception e) {
                AppLogger.NOTE.e(TAG, "registerStatubarReceiver error e = " + e.getMessage());
            }
            mRegistFlag = true;
        }
    }

    private void unregisterStatubarReceiver() {
        if (!mEnableStatusBarReceiver) {
            return;
        }
        if (mRegistFlag) {
            unregisterReceiver(mStatubarClickReceiver);
            mRegistFlag = false;
        }
    }

    @Override
    public void onDialogClickButton(int type, int index) {
        //Subclass implementation
    }

    @Override
    public void onDialogClickPositive(int type) {
        //Subclass implementation
    }

    @Override
    public void onDialogClickNegative(int type) {
        //Subclass implementation
    }

    @Override
    public void onDialogDismiss(int type) {
        //Subclass implementation
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mDialogFactory != null) {
            mDialogFactory.onConfigurationChanged(newConfig);
        }
        setScreenOrientation();
    }

    //mode 100 分屏
    //mode fullscreen 全屏
    public static String getMode(String configStr) {
        try {
            return configStr.split("mWindowingMode=")[1].split(" ")[0];
        } catch (Exception e) {
            return "100";
        }
    }

    protected boolean isVaildContext(Activity activity) {
        return (null != activity) && (!activity.isDestroyed()) && (!activity.isFinishing());
    }

    public View getStatusBarView() {
        //add for immersive theme. statusbar placeholder view
        int statusHeight = WindowInsetsUtil.getStatusBarHeight(getBaseContext());
        ImageView imageView = new ImageView(getBaseContext());
        imageView.setBackgroundColor(getBaseContext().getColor(R.color.window_background_color));
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);
        imageView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, statusHeight));

        return imageView;
    }

    private COUIBottomSheetDialogFragment mBottomSheetDialogFragment = null;
    private final String DIALOG_FRAGMENT_TAG = "bottom sheet";

    public void showPanelFragment(COUIPanelFragment panelFragment) {
        if (mBottomSheetDialogFragment != null) {
            try {
                mBottomSheetDialogFragment.dismiss();
            } catch (IllegalStateException e) {
                AppLogger.BASIC.e(TAG, "showPanelFragment, dismiss error: " + e);
            }
        }
        mBottomSheetDialogFragment = new NoteCOUIBottomSheetDialogFragment();
        mBottomSheetDialogFragment.setMainPanelFragment(panelFragment);
        if (!isFinishing() && getWindow() != null) {
            mBottomSheetDialogFragment.show(getSupportFragmentManager(), DIALOG_FRAGMENT_TAG);
        } else {
            AppLogger.BASIC.d(TAG, "activity is finish");
        }
    }

    protected void setEnableStatusBarReceiver(boolean enable) {
        mEnableStatusBarReceiver = enable;
    }
}
