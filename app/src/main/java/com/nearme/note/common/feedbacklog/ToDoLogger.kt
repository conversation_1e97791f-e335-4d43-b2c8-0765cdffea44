package com.nearme.note.common.feedbacklog

import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.logger.AppLogger

/**
 * Created by 80326921 on 2021/4/30.
 * Email
 */
object ToDoLogger {

    fun printLog(operation: FeedbackLog.Operation, data: ToDo?) {
        data?.let {
            FeedbackLog.D.dbLog(operation, "ToDo", it.localId.toString(), it.content)
        }
    }

    fun printLog(operation: FeedbackLog.Operation, dataList: List<ToDo>?) {
        try {
            dataList?.let {
                for (data in it) {
                    FeedbackLog.D.dbLog(operation, "ToDo", data.localId.toString(), data.content)
                }
            }
        } catch (e: Exception) {
            AppLogger.BASIC.e("printLog", e.message)
        }
    }

    fun printLog(msg: String) {
        FeedbackLog.D.anyDbLog(msg)
    }

}