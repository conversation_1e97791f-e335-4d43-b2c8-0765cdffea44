/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ThirdLogObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  nieyong       2024/1/17     1.0     create file
 ****************************************************************/
package com.nearme.note.util

import com.nearme.note.MyApplication
import com.oplus.note.data.third.ThirdLogParagraph
import com.nearme.note.thirdlog.ThirdLogParser
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import java.io.File

object LrcUtils {

    private const val TAG = "lrcUtils"

    @JvmStatic
    fun getSpeechLogInfoList(lrcAttachment: Attachment): List<ThirdLogParagraph>? {
        if (lrcAttachment.type != Attachment.TYPE_SPEECH_LRC && lrcAttachment.type != Attachment.TYPE_IDENTIFY_VOICE_LRC) {
            AppLogger.BASIC.d(TAG, "getSpeechLogInfoList Attachment type is not match!${lrcAttachment.type}")
            return null
        }
        val path = lrcAttachment.absolutePath(MyApplication.appContext)
        val file = File(path)
        if (file.exists()) {
            val thirdLog = ThirdLogParser.parseThirdLogFromFile(file)
            return if (thirdLog == null) {
                null
            } else {
                val list = thirdLog.thirdLogParagraph
                if (list.isEmpty()) {
                    null
                } else {
                    val speechStartTime = thirdLog.speechStartTime
                    val baseTime = if (speechStartTime == 0L) {
                        list[0].startTime
                    } else {
                        speechStartTime
                    }
                    for (i in list.indices) {
                        list[i].startTime -= baseTime
                        list[i].endTime -= baseTime
                    }
                    list
                }
            }
        } else {
            return null
        }
    }
}