/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: Extensions.kt
 * * Description: Extensions
 * * Version: 1.0
 * * Date: 2020/08/04
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.Context
import android.content.ContextWrapper
import android.graphics.Rect
import android.media.ExifInterface
import android.os.Build
import android.os.Looper
import android.os.Parcel
import android.os.Parcelable
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.PopupWindow
import android.widget.TextView
import androidx.annotation.IntRange
import androidx.core.graphics.drawable.IconCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.RichSearchText
import com.oplus.anim.EffectiveAnimationView
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import java.io.File

//----------------- context extensions -----------------------
/**
 * Return true if this [Context] is available.
 * Availability is defined as the following:
 * + [Context] is not null
 * + [Context] is not [Application]
 * + [Context] is not destroyed (tested with [FragmentActivity.isDestroyed] or [Activity.isDestroyed])
 */
fun Context?.isAvailableForGlide(): Boolean {
    return when (this) {
        is Application -> false
        is Activity -> !isDestroyed and !isFinishing
        is ContextWrapper -> baseContext.isAvailableForGlide()
        else -> false
    }
}

fun Context.glideWithAvailable(): RequestManager? {
    return if (this.isAvailableForGlide()) {
        Glide.with(this)
    } else {
        null
    }
}

fun PopupWindow?.safeDismiss() {
    this?.run {
        if (isShowing) dismiss()
    }
}


fun ExifInterface?.exifOrientation(): Int {
    return when (this?.getAttributeInt(ExifInterface.TAG_ORIENTATION, 0)) {
        ExifInterface.ORIENTATION_ROTATE_90 -> 90
        ExifInterface.ORIENTATION_ROTATE_180 -> 180
        ExifInterface.ORIENTATION_ROTATE_270 -> 270
        else -> 0
    }
}

fun ExifInterface?.pictureInfo(): IntArray {
    val degree = this?.exifOrientation() ?: 0
    val width = this?.getAttributeInt(ExifInterface.TAG_IMAGE_WIDTH, 0) ?: 0
    val height = this?.getAttributeInt(ExifInterface.TAG_IMAGE_LENGTH, 0) ?: 0
    return intArrayOf(degree, width, height)
}

fun Context.filesDirAbsolutePath(): String {
    return MyApplication.myApplication.filesDirAbsolutePath
}

fun filesDirAbsolutePath(): String {
    return MyApplication.myApplication.filesDirAbsolutePath
}

fun String.subName(): String {
    val index = lastIndexOf(".")
    return if (index >= 0) {
        substring(0, index)
    } else {
        this
    }
}

fun String.suffix(): String {
    val index = lastIndexOf(".")
    return if (index >= 0) {
        substring(index + 1)
    } else {
        ""
    }
}

fun File.createFile(): File? {
    parentFile?.createDirectory() ?: return null
    if (!exists() && !createNewFile()) {
        AppLogger.BASIC.e(TAG, "create file error. path=$path")
        return null
    }
    return this
}

fun File.createDirectory(): File? {
    if (!exists() && !mkdirs()) {
        AppLogger.BASIC.e(TAG, "create directory error. path=$path")
        return null
    }
    return this
}

fun Int.minusColorAlpha(@IntRange(from = 0, to = 255) alpha: Int): Int {
    return this shl 8 ushr 8 or (alpha shl 24)
}

fun File?.notNullAndExists(): Boolean {
    return this?.exists() ?: false
}

fun Context.getApplicationIcon(getLocal: Boolean = false): IconCompat {
    return if (getLocal) {
        IconCompat.createWithResource(this, R.drawable.ic_launcher_nearme_note)
    } else {
        runCatching {
            IconCompat.createWithBitmap(packageManager.getApplicationIcon(packageName).toBitmap())
        }.getOrElse {
            AppLogger.BASIC.d("getApplicationIcon", "PackageManager.NameNotFoundException")
            IconCompat.createWithResource(this, R.drawable.ic_launcher_nearme_note)
        }
    }
}

/**
 * 将像素值转换为 dp 值，带保护性检查
 */
fun Context.px2dp(pxValue: Float): Int {
    val scale = resources.displayMetrics.density
    val result = if (scale > 0) {
        DensityHelper.px2dip(this, pxValue)
    } else {
        // 如果密度异常，使用默认转换
        (pxValue / 3.0f + 0.5f).toInt() // 假设 3x 密度
    }
    AppLogger.BASIC.d("Extensions", "px2dp: pxValue=$pxValue, scale=$scale, result=$result")
    return maxOf(0, result) // 确保结果不为负数
}

fun Dialog.startRotatingAnimation(loadingText: String = "") {
    setCanceledOnTouchOutside(false)
    window?.decorView?.apply {
        findViewById<TextView>(com.oplusos.vfxsdk.doodleengine.R.id.progress_tips)?.text = loadingText
        val rotatingView = findViewById<EffectiveAnimationView>(R.id.progress)
        viewTreeObserver?.addOnWindowAttachListener(object :
            ViewTreeObserver.OnWindowAttachListener {
            override fun onWindowAttached() {
                rotatingView?.playAnimation()
            }

            override fun onWindowDetached() {
                rotatingView?.pauseAnimation()
                viewTreeObserver?.removeOnWindowAttachListener(this)
            }
        })
    }
}

/**
 * 指定权限是否勾选了不再提醒.
 *
 * @param permission 待判断的权限
 * @return true 表示已勾选［不再提醒］, false　表示未勾选［不再提醒］
 */
fun Activity.isNotRemind(permission: String) = !shouldShowRequestPermissionRationale(permission)

/**
 * 指定权限是否勾选了不再提醒.
 *
 * @param permission 待判断的权限
 * @return true 表示已勾选［不再提醒］, false　表示未勾选［不再提醒］
 */
fun Fragment.isNotRemind(permission: String) = !shouldShowRequestPermissionRationale(permission)

fun MutableLiveData<Long>.getValueWithDefault(): Long = value ?: 0

fun <T> MutableLiveData<T>.postValueSafe(t: T?) {
    if (Looper.getMainLooper()?.thread?.id == Thread.currentThread().id) {
        value = t
    } else {
        postValue(t)
    }
}

fun Long?.durationInMsFormatTimeExclusive(leastOneSecond: Boolean = false): String {
    val duration = (this ?: 0)
    return TimeUtils.getFormatTimeExclusiveMill(duration, leastOneSecond)
}

fun Long?.currentInMsFormatTimeExclusive(duration: Long?): String {
    val currentTime = (this ?: 0)
    val durationTime = (duration ?: 0)
    return TimeUtils.getFormatTimeExclusiveMill(
        currentTime,
        (durationTime > 0) && (durationTime < TimeUtils.TIME_ONE_SECOND) && (durationTime - currentTime < TimeUtils.TIME_MS_100)
    )
}
/**
 * 在textView中根据下标计算出对应文字所在的位置
 * @param startIndex 计算文字的起始下标
 * @return Int 返回具体的位置bottom
 */
fun TextView.calculateTextScrollOffset(startIndex: Int?): Int {
    AppLogger.BASIC.e(RichSearchText.TAG, "calculateTextScrollOffset startIndex:$startIndex")
    val line = layout?.getLineForOffset(startIndex ?: 0) //获取字符在第几行
    val bound = Rect()
    if (line != null) {
        layout.getLineBounds(line, bound)
    }
    return bound.bottom
}

@Suppress("DEPRECATION")
fun <T : Parcelable> T.newInstance(): T {
    var result: T? = null
    var parcel: Parcel? = null
    runCatching {
        parcel = Parcel.obtain().apply {
            writeParcelable(this@newInstance, 0)
            setDataPosition(0)
        }
        result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            parcel?.readParcelable(this::class.java.classLoader, this::class.java)
        } else {
            parcel?.readParcelable(this::class.java.classLoader)
        }
    }.onFailure {
        AppLogger.BASIC.e("Extensions", "deepCopy e:$it")
    }
    parcel?.recycle()
    return result ?: this
}

/**
 * Executes [block] with the View's margin layoutParams and reassigns the layoutParams with the
 * updated version.
 *
 * @see View.getLayoutParams
 * @see View.setLayoutParams
 **/
inline fun View.updateMarginLayoutParams(block: ViewGroup.MarginLayoutParams.() -> Unit) {
    updateMarginLayoutParams<ViewGroup.MarginLayoutParams>(block)
}

/**
 * Executes [block] with a typed version of the View's margin layoutParams and reassigns the
 * layoutParams with the updated version.
 *
 * @see View.getLayoutParams
 * @see View.setLayoutParams
 **/
@JvmName("updateMarginLayoutParamsTyped")
inline fun <reified T : ViewGroup.MarginLayoutParams> View.updateMarginLayoutParams(
    block: T.() -> Unit
) {
    val params = layoutParams
    if (params is T) {
        block(params)
        layoutParams = params
    }
}