/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: MARKER.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/6
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.paint.view

import androidx.collection.arrayMapOf
import com.oplus.note.R


class Marker : Pen {
    override var strokeWidth: Float = 32F
    override var stroke: Stroke = Stroke.TYPE3
        set(value) {
            field = value
            strokeWidth = when (value) {
                Stroke.TYPE1 -> {
                    0F
                }
                Stroke.TYPE2 -> {
                    16F
                }
                Stroke.TYPE3 -> {
                    32F
                }
                Stroke.TYPE4 -> {
                    56F
                }
                Stroke.TYPE5 -> {
                    80F
                }
            }
        }

    override var alpha: Int = 255

    override var color: Int = PaintConstants.MARKER_COLOR.toInt()

    override val strokeImageResources: Map<Stroke, Int>
        get() = arrayMapOf(
                Stroke.TYPE1 to R.drawable.paint_ic_stroke_marker_1,
                Stroke.TYPE2 to R.drawable.paint_ic_stroke_marker_2,
                Stroke.TYPE3 to R.drawable.paint_ic_stroke_marker_3,
                Stroke.TYPE4 to R.drawable.paint_ic_stroke_marker_4,
                Stroke.TYPE5 to R.drawable.paint_ic_stroke_marker_5,
        )

    var isLightColor = false
}