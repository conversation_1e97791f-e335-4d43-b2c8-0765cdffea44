/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - IndividualDoodleAction
 ** Description:
 **         v1.0:   start individual doodle on light model
 **
 ** Version: 1.0
 ** Date: 2024/12/30
 ** Author: Ji<PERSON><PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.Yan              2024/12/30        1.0      Create this module
 ********************************************************************************/
package com.nearme.note.external.action.impl

import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.external.result.impl.IndividualDoodleResultHandler
import com.nearme.note.paint.PaintActivity
import com.nearme.note.paint.PaintFragment
import com.nearme.note.util.ConfigUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class IndividualDoodleAction(val fragment: WVNoteViewEditFragment) :
    AbstractActivityResultLauncherAction<Intent>(fragment.activity?.activityResultRegistry, fragment) {

    /**
     * Legacy logic startActivityForResult$requestCode
     */
    private val resultHandler by lazy {
        IndividualDoodleResultHandler(fragment)
    }

    override fun onCreateInput(bundle: Bundle?): Intent? {
        val ctx = fragment.context ?: return null

        val intent = Intent(ctx, PaintActivity::class.java)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        intent.putExtra(PaintFragment.KEY_HASH_CODE, fragment.mViewModel.hashCode)
        intent.putExtra(PaintFragment.KEY_EXTRA_TWOPANE, fragment.twoPane)

        return intent
    }

    override fun onCreateOutput(): ActivityResultContract<Intent, ActivityResult> {
        return ActivityResultContracts.StartActivityForResult()
    }

    override suspend fun onHandleResult(result: ActivityResult) {
        resultHandler.handleResult(result.data, false, true, true)
    }

    override fun onHandleResultCancel(data: Intent?) {
        super.onHandleResultCancel(data)
        if (!ConfigUtils.isSupportOverlayPaint) {
            fragment.changePaintButtonLightOS(data)
        }
    }

    override fun resetInsertAttach() {
        fragment.mViewModel.isInsertAttachFromEditMode = null
    }

    fun handleReceiverPaintResult(intent: Intent) {
        fragment.lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                resultHandler.handleResult(intent, false, true, true)
            }
            onEnd()
        }
    }

    companion object {
        private const val TAG = "IndividualDoodleAction"
    }
}