package com.nearme.note.util;

import android.content.Context;

import androidx.test.core.app.ApplicationProvider;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class NoteColorTextUtilsTest {
    private static String sEmojiString = "⭕|⭐|[\uD83C\uDC00-\uD83C\uDFFF]|[\uD83D\uDC00-\uD83D\uDFFF]|[☀-⟿]" +
            "|[\uD83E\uDD10-\uD83E\uDDFF]|[☀-⟿]|[\uE000-\uF8FF]|\\ufe0f|\\u25fd|\\u25fe|\\u2b1b|\\u2b1c|\\u23e9" +
            "|\\u23ea|\\u23eb|\\u23ec|\\u231b|\\u23f3|\\u23f9|\\u23f8|\\u23fa|\\u231a|\\u23f0|\\u20e3";
    private Context mContext;
    private CharSequence mTestString;
    private String mSourceString;
    private String mUnicodeText = "\\u74\\u65\\u73\\u74";
    private String mIllegalChar = ".*[\\\\/*:：?？<>|\"]+?.*";
    private String mText = "test";
    private String mNull = "";

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_getBoolean_when_containsIllegalCharFileName_with_source() {
        mSourceString = mNull;
        Assert.assertFalse(NoteColorTextUtils.containsIllegalCharFileName(mSourceString));
        mSourceString = mText;
        Assert.assertFalse(NoteColorTextUtils.containsIllegalCharFileName(mSourceString));
        mSourceString = mIllegalChar;
        Assert.assertTrue(NoteColorTextUtils.containsIllegalCharFileName(mSourceString));
    }

    @Test
    public void should_return_boolean_when_lengthLimit_with_charSequence_maxLength() {
        int length = 10;
        Assert.assertFalse(NoteColorTextUtils.lengthLimit(null, length));
        Assert.assertFalse(NoteColorTextUtils.lengthLimit(mNull, length));
        Assert.assertFalse(NoteColorTextUtils.lengthLimit(mText, length));
        mText = "01234567890";
        Assert.assertTrue(NoteColorTextUtils.lengthLimit(mText, length));
    }
}