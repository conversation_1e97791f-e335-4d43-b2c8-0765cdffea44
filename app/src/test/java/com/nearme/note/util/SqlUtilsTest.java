package com.nearme.note.util;

import static org.junit.Assert.*;

import android.util.ArraySet;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)

public class SqlUtilsTest {
    @Test
    public void should_return_when_joinIds_with_ids_set(){
        Set<String> strings = new ArraySet<>();
        strings.add("aa");
        strings.add("bb");
        String s = SqlUtils.joinIds(strings);
        assertEquals("'aa','bb'", s);
    }

    @Test
    public void should_return_when_joinIds_with_ids_list(){
        List<String> strings = new ArrayList<>();
        strings.add("aa");
        strings.add("bb");
        String s = SqlUtils.joinIds(strings);
        assertEquals("'aa','bb'", s);
    }
    @Test
    public void should_sqliteEscape(){
        String keyWord = "/'[]%&_()";
        String result = SqlUtils.sqliteEscape(keyWord);
        assertNotEquals(result,"//''/[/]/%/&/_/(/)");
    }

}