package com.nearme.note.util;

import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class EnvirStateUtilsTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = RuntimeEnvironment.application;
    }

    @Test
    public void should_changeComponentState_with_context_and_isOpen_and_cls() {
        Class<EnvirStateUtils> cls = null;
        Assert.assertNull(null);
        EnvirStateUtils.changeComponentState(null, false, null);
        EnvirStateUtils.changeComponentState(null, true, null);
        cls = EnvirStateUtils.class;

        PackageManager mPackageManager = Mockito.mock(PackageManager.class);
        Assert.assertNotNull(mContext);
        Assert.assertNotNull(cls);
        EnvirStateUtils.changeComponentState(mContext, false, cls);
        EnvirStateUtils.changeComponentState(mContext, true, cls);

        Assert.assertEquals(0, mPackageManager.getComponentEnabledSetting(new ComponentName(mContext, cls)));

    }

    @Test
    public void should_getComponentState_with_context_and_cls() {
        Class<EnvirStateUtils> cls = null;
        Assert.assertNull(null);
        Assert.assertFalse(EnvirStateUtils.getComponentState(null, null));
        cls = EnvirStateUtils.class;
        Assert.assertNotNull(mContext);
        Assert.assertNotNull(cls);
        Assert.assertTrue(EnvirStateUtils.getComponentState(mContext, cls));
    }

    @Test
    public void should_checkSettingEntranceState_with_context() {
        Assert.assertNull(null);
        EnvirStateUtils.checkSettingEntranceState(null);
        Assert.assertNotNull(mContext);
    }

}
