package com.nearme.note.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.oplus.note.R
import org.junit.Assert
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class BitmapUtilTest {

    val mContext = RuntimeEnvironment.application.applicationContext

    @Test
    fun `should return not null when decodeRes`() {
        val decodeRes = BitmapUtil.decodeRes(mContext, R.drawable.note_color_menu_ic_add_disabled, null)
        Assert.assertNotNull(decodeRes)
    }

    @Test
    fun `should return not null when scaleBitmap`() {
        val bitmap = BitmapUtil.scaleBitmap(null, 10, 10)
        Assert.assertNull(bitmap)
        val origin = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val scaleBitmap = BitmapUtil.scaleBitmap(origin, 10, 10)
        Assert.assertNotNull(scaleBitmap)

    }

    @Test
    fun `should return not null when adjustBitmapToFullScreen`() {
        val srcBitmap = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val adjustBitmapToFullScreen = BitmapUtil.adjustBitmapToFullScreen(mContext, 10, 10, srcBitmap)
        Assert.assertNotNull(adjustBitmapToFullScreen)

        val bitmap = BitmapUtil.adjustBitmapToFullScreen(mContext, 5, 5, srcBitmap)
        Assert.assertNotNull(bitmap)
    }

    @Test
    fun `should not exception when crop`() {
        val src = BitmapFactory.decodeResource(mContext.resources, R.drawable.note_color_menu_ic_add_disabled)
        val w = 10
        val h = 10
        val horizontalCenterPercent = 1.00f
        val verticalCenterPercent = 1.00f
        val cached = try {
            BitmapUtil.crop(src, w, h, horizontalCenterPercent, verticalCenterPercent)
            null
        } catch (e:IllegalArgumentException){
            1
        }
        Assert.assertNull(cached)
    }
    @Test
    fun `should return null when getRoundBitmapByShader if bitmap is null`() {
        val bitmap = null
        val roundBitmapByShader = BitmapUtil.getRoundBitmapByShader(bitmap, 50, 50, 1, 1)
        Assert.assertNull(roundBitmapByShader)
    }

    @Test
    fun `should return not null when getRoundBitmapByShader if bitmap is not null`() {
        val bitmap = mockk<Bitmap>(relaxed = true, relaxUnitFun = true)
        val roundBitmapByShader = BitmapUtil.getRoundBitmapByShader(bitmap, 50, 50, 1, 1)
        Assert.assertNotNull(roundBitmapByShader)
    }

    @Test
    fun `should saved width and height when getPictureSize`() {
        val wh = IntArray(2)
        BitmapUtil.getPictureSize("app/src/main/res/drawable-nodpi/bg_note_widget_picture_count.9.png", wh)
        Assert.assertNotNull(wh[0])
        Assert.assertNotNull(wh[1])
    }
}