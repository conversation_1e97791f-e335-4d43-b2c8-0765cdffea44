package com.nearme.note.activity.richedit.webview

import android.content.Context
import com.nearme.note.util.WindowInsetsUtil
import com.oplus.note.logger.AppLogger
import io.mockk.*
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 WVRichEditorCoverPaint 中的密度计算修复
 */
class WVRichEditorCoverPaintTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk(relaxed = true)
        mockkStatic(WindowInsetsUtil::class)
        mockkStatic(AppLogger::class)
        every { AppLogger.BASIC.d(any(), any()) } just Runs
    }

    @Test
    fun `test defaultDensity calculation with normal values`() {
        // 模拟正常的密度值
        every { WindowInsetsUtil.getDefaultDensity() } returns 480

        val editor = WVRichEditorCoverPaint(context)
        
        // 通过反射访问 defaultDensity
        val defaultDensityField = WVRichEditorCoverPaint::class.java.getDeclaredField("defaultDensity")
        defaultDensityField.isAccessible = true
        val defaultDensity = (defaultDensityField.get(editor) as Lazy<Float>).value

        // 480 / 160 = 3.0
        assertEquals(3.0f, defaultDensity, 0.001f)
    }

    @Test
    fun `test defaultDensity calculation with negative density`() {
        // 模拟 WindowInsetsUtil.getDefaultDensity() 返回 -1 的情况
        every { WindowInsetsUtil.getDefaultDensity() } returns -1

        val editor = WVRichEditorCoverPaint(context)
        
        // 通过反射访问 defaultDensity
        val defaultDensityField = WVRichEditorCoverPaint::class.java.getDeclaredField("defaultDensity")
        defaultDensityField.isAccessible = true
        val defaultDensity = (defaultDensityField.get(editor) as Lazy<Float>).value

        // 应该使用默认值 1.0f 而不是负数
        assertEquals(1.0f, defaultDensity, 0.001f)
    }

    @Test
    fun `test defaultDensity calculation with zero density`() {
        // 模拟密度为 0 的情况
        every { WindowInsetsUtil.getDefaultDensity() } returns 0

        val editor = WVRichEditorCoverPaint(context)
        
        // 通过反射访问 defaultDensity
        val defaultDensityField = WVRichEditorCoverPaint::class.java.getDeclaredField("defaultDensity")
        defaultDensityField.isAccessible = true
        val defaultDensity = (defaultDensityField.get(editor) as Lazy<Float>).value

        // 应该使用默认值 1.0f
        assertEquals(1.0f, defaultDensity, 0.001f)
    }

    @Test
    fun `test webview layout params with fixed density`() {
        // 模拟正常的密度值
        every { WindowInsetsUtil.getDefaultDensity() } returns 480

        val editor = WVRichEditorCoverPaint(context)
        
        // 通过反射访问 getWebViewLayoutParams 方法
        val getWebViewLayoutParamsMethod = WVRichEditorCoverPaint::class.java.getDeclaredMethod("getWebViewLayoutParams")
        getWebViewLayoutParamsMethod.isAccessible = true
        val layoutParams = getWebViewLayoutParamsMethod.invoke(editor) as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

        // 验证宽度是正数
        assertTrue("WebView width should be positive", layoutParams.width > 0)
    }
}
