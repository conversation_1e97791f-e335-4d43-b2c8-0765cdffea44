package com.nearme.note.activity.notebook

import com.oplus.note.notebook.internal.NoteBookData
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class NoteBookDataTest {


    @Test
    fun `should getAllNoteDefaultCover`(){
        Assert.assertNotNull(NoteBookData.getAllNoteDefaultCover())
    }
    @Test
    fun `should getDefaultCover`(){
        Assert.assertNotNull(NoteBookData.getDefaultCover())
    }
}