/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * File: EmbedSkinInitializerTest.kt
 * * Description: EmbedSkinInitializerTest
 * * Version: 1.0
 * * Date: 2023/01/29
 * * Author: hurui
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.skin.api

import android.util.ArrayMap
import com.nearme.note.skin.SkinData
import com.nearme.note.skin.bean.Skin
import com.nearme.note.skin.bean.SkinSummary
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class EmbedSkinInitializerTest {

    @Before
    fun setUp() {
        mockkStatic(SkinData::class)
        mockkObject(EmbedSkinInitializer)
    }

    @After
    fun tearDown() {
        unmockkStatic(SkinData::class)
        unmockkObject(EmbedSkinInitializer)
    }

    @Test
    fun should_init_skin_summary_when_initiateSkinSummaries() {
        val embedSkinSummaries = ArrayList<SkinSummary>()

        SkinData.isAddManualSkin = true
        EmbedSkinInitializer.initiateSkinSummaries(embedSkinSummaries)
        assertEquals(6, embedSkinSummaries.size)

        embedSkinSummaries.clear()
        SkinData.isAddManualSkin = false
        EmbedSkinInitializer.initiateSkinSummaries(embedSkinSummaries)
        assertEquals(1, embedSkinSummaries.size)
    }

    @Test
    fun should_init_skin_when_initiateSkins() {
        val embedSkins = ArrayMap<String, Skin>()

        SkinData.isAddManualSkin = true
        EmbedSkinInitializer.initiateSkins(embedSkins)
        assertEquals(5, embedSkins.size)

        embedSkins.clear()
        SkinData.isAddManualSkin = false
        EmbedSkinInitializer.initiateSkins(embedSkins)
        assertEquals(2, embedSkins.size)
    }
}