/***********************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: DatabaseHelperTest.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/10/3
 * * Author:
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloud.sync.note

import android.database.sqlite.SQLiteDatabase
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class DatabaseHelperTest {
    private var mDatabaseHelper: DatabaseHelper? = null
    @Before
    fun setUp() {
        mDatabaseHelper = DatabaseHelper.instance
    }

    @Test
    fun should_onCreate_with_SQLiteDatabase() {
        val mSQLiteDatabase = Mockito.mock(SQLiteDatabase::class.java)
        mDatabaseHelper!!.onCreate(mSQLiteDatabase)
        Assert.assertNotNull(mSQLiteDatabase)
    }
}