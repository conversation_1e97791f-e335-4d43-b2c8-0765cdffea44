/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/10/17      1.0     create file
 ****************************************************************/
package com.oplus.note.service.summarynote;

interface IClientCallback {
    /**
     * 重新生成摘要
     */
    boolean recreateSummary(String noteId,boolean autoJoin);
    /**
     * 停止生成摘要
     */
    boolean stopCreateSummary(String noteId);
    /**
     * 摘要文件拷贝结束
     */
    boolean onSummaryFileCopyEnd(String noteId);
    /**
    * 指定的功能当前是否支持(端侧化需求中新增)
    *  重新生成："recreate_summary"
    *  停止生成: "stop_summary"
    */
    boolean isFunctionSupport(String noteId,String funcationId);
}