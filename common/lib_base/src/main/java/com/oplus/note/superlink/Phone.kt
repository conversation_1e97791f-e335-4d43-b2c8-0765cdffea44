/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: Phone.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/24
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.superlink

import android.content.Context
import androidx.appcompat.content.res.AppCompatResources
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.note.logger.AppLogger
import com.oplus.note.statistic.StatisticsCallSummary
import com.oplus.note.utils.copyToClipboard

class Phone : SuperLinkMaker {

    companion object {
        private const val TAG = "SuperLinkMaker.Phone"
        private var sIsClickItem = false
    }

    override fun create(context: Context, data: String, fromType: Int): COUIPopupListWindow {
        AppLogger.BASIC.d(TAG, "create PopWindow")

        val vPhoneNum = data.replace("[^0-9\\+]".toRegex(), "")

        val itemsRes = context.resources.getStringArray(com.oplus.note.baseres.R.array.usemainnumexist_dialog_items)
        val items = mutableListOf<PopupListItem>().apply {
            if (isSupportDial(context)) {
                add(
                    PopupListItem(
                        AppCompatResources.getDrawable(
                            context,
                            com.oplus.note.baseres.R.drawable.note_ic_phone
                        ), itemsRes[ITEM_POSITION_0], true
                    )
                )
            }
            if (isSupportSendSms(context)) {
                add(
                    PopupListItem(
                        AppCompatResources.getDrawable(
                            context,
                            com.oplus.note.baseres.R.drawable.note_ic_message
                        ), itemsRes[ITEM_POSITION_1], true
                    )
                )
            }
            if (isSupportSaveToContact(context)) {
                add(
                    PopupListItem(
                        AppCompatResources.getDrawable(
                            context,
                            com.oplus.note.baseres.R.drawable.note_ic_contact
                        ), itemsRes[ITEM_POSITION_2], true
                    )
                )
            }
            add(
                PopupListItem(
                    AppCompatResources.getDrawable(
                        context,
                        com.oplus.note.baseres.R.drawable.note_ic_copy
                    ), itemsRes[ITEM_POSITION_3], true
                )
            )
        }

        val popup = COUIPopupListWindow(context)
        popup.setDismissTouchOutside(true)
        popup.itemList = items
        popup.setOnItemClickListener { _, _, position, _ ->
            when (itemsRes.indexOf(items[position].title)) {
                ITEM_POSITION_0 -> {
                    dialPhoneNumber(context, vPhoneNum)
                    StatisticsCallSummary.setEventPhoneLinkClick(fromType, StatisticsCallSummary.PhoneLinkClickType.CALL_PHONE, context)
                }

                ITEM_POSITION_1 -> {
                    sendSms(context, vPhoneNum)
                    StatisticsCallSummary.setEventPhoneLinkClick(fromType, StatisticsCallSummary.PhoneLinkClickType.SEND_MSG, context)
                }

                ITEM_POSITION_2 -> {
                    savePhoneNumber(context, vPhoneNum)
                    StatisticsCallSummary.setEventPhoneLinkClick(fromType, StatisticsCallSummary.PhoneLinkClickType.SAVE_NUM, context)
                }

                ITEM_POSITION_3 -> {
                    copyToClipboard(context, vPhoneNum)
                    StatisticsCallSummary.setEventPhoneLinkClick(fromType, StatisticsCallSummary.PhoneLinkClickType.COPY_NUM, context)
                }

                else -> {}
            }
            sIsClickItem = true
            popup.dismiss()
        }
        popup.setOnDismissListener {
            if (!sIsClickItem) {
                StatisticsCallSummary.setEventPhoneLinkClick(
                    fromType,
                    StatisticsCallSummary.PhoneLinkClickType.CANCEL,
                    context
                )
            }
            sIsClickItem = false
        }
        return popup
    }
}