/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper

fun <R> Context?.ifIsActivity(block: (Activity) -> R): R? {
    return this?.let {
        when (this) {
            is Activity -> block(this)
            is ContextWrapper -> this.baseContext.ifIsActivity(block)
            else -> {
                // do nothing
                null
            }
        }
    }
}