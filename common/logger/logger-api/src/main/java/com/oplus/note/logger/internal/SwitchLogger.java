/****************************************************************
 * * Copyright (C), 2020-2028, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SwitchLogger.java
 * * Description: SwitchLogger
 * * Version: 1.0
 * * Date: 2020/3/2
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2020/3/2 1.0 build this module
 ****************************************************************/
package com.oplus.note.logger.internal;

import com.oplus.note.logger.BuildConfig;
import com.oplus.note.logger.LinkedLogger;
import com.oplus.note.osdk.proxy.OplusBuildProxy;
import com.oplus.note.osdk.proxy.OplusSystemPropertiesProxy;

public class SwitchLogger extends LinkedLogger {

    private static final int LEVEL;
    private final boolean mEnable;

    static {
        boolean enable = OplusSystemPropertiesProxy.INSTANCE.isLogKitEnable();
        if (enable) {
            LEVEL = android.util.Log.VERBOSE;
        } else {
            LEVEL = android.util.Log.WARN;
        }
    }

    public SwitchLogger() {
        this(true);
    }

    public SwitchLogger(boolean enable) {
        this.mEnable = enable;
    }

    @Override
    public void log(int priority, String tag, String message) {
        if (BuildConfig.DEBUG || OplusBuildProxy.INSTANCE.isAboveOS132()) {
            mNext.log(priority, tag, message);
        } else if (mEnable && (priority >= LEVEL)) {
            mNext.log(priority, tag, message);
        }
    }
}
