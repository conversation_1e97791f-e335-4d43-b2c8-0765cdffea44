<template>
    <card-template entry="desktop">
        <div class="div containerBg">
            <!-- 无权限或笔记本 -->
            <div class="containerTvBtn" if="{{isNoPermissionsOrNotebook}}"> 
                <div class="containerTvBtnTv">
                    <text class="containerTvBtnText colorTips">{{tipsStr}}</text>
                </div>
                <button class="btn btnBg" onclick="selectNotebookClick" if="{{isNoNotebook}}">{{checkStr}}</button>
                <button class="btn btnBg" onclick="permissionsClick" else>{{checkStr}}</button>
            </div>
        </div>
    </card-template>
</template>
<style>
.container {
    width: 100%;
    height: 100%;
    flex-direction: column;
}
.containerBg {
    background-image: "{{ $r('images.mainBg') }}";
    background-size: 100%;
}
.containerTop {
    width: 100%;
    height: 36px;
    margin-start: 16px;
    padding-top: 12px;
    align-items: flex-start;
}
.containerBottom {
    width: 100%;
    height: 100%;
    position: absolute;
    margin-top: 39px;
    margin-bottom: 14px;
}
.containerTvBtn {
    width: 100%;
    height: 100%;
}
.containerTvBtnTv {
    width: 100%;
    height: 100%;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    transform: translateY(-12px);
}
.containerTvBtnText {
    width: 100%;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    max-lines: 2;
}
.containerOneData {
    width: 100%;
    height: 100%;
    margin-start: 16px;
    margin-end: 16px;
    margin-top: 8px;
    flex-direction: column;
}
.containerList {
    width: 100%;
    height: 100%;
    margin-start: 16px;
    margin-end: 16px;
    flex-direction: column;
}
.containerItem {
    width: 100%;
    height: 50%;
    flex-direction: column;
}
.containerItemCenter{
    width: 100%;
    height: 100%; 
    flex-direction: column;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}
.containerNoNote {
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: absolute;
}
.div {
    width: 100%;
    height: 100%;
}
.center {
    align-items: center;
}
.text {
    width: 100%;
    text-overflow: ellipsis;
}
.textListTitle {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-overflow: ellipsis;
}
.textListContent {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-overflow: ellipsis;
}
.textNote {
    font-size: 12px;
    font-weight: 500;
    margin-start: 6px;
    margin-left: 8px;
    max-lines: 1;
}
.textNotebook {
    width: 260px;
    font-size: 14px;
    margin-top: 4px;
    font-weight: 600;
    max-lines: 1;
}
.textNoteTitle {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-overflow: ellipsis;
}
.textNoteContent {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
}
.textMaxOne {
    max-lines: 1;
}
.textMaxThree {
    max-lines: 3;
}
.colorTextNoteTitle {
    color: #E6000000;
}
.colorTextNoteContent {
    color: #8A000000;
}
.textContentMsg {
    font-size: 12px;
    font-weight: 400;
    margin-top: 4px;
    max-lines: 1;
}
.colortTextContent {
    color: #8A000000;
}
.textNoNote {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    line-height: 16px;
}
.textTime{
    position: absolute;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    bottom: 0px;
}
.colorTextNoteBook {
    color: #E6000000;
}
.imgNotebook {
    margin-start: 18px;
    margin-top: 5px;
    width: 12px;
    height: 14px;
}
.img {
    width: 36px;
    height: 36px;
    border-radius: 6px;
}
.imgDiv {
    width: 100%;
    height: 100%;
    position: absolute;
    align-items: center;
    justify-content: flex-end;
}
.imgBig {
    width: 68.57px;
    height: 40px;
    margin-top: 6px;
    border-radius: 6px;
    object-fit: cover;
}
.imgAddNote {
    width: 24px;
    height: 24px;
    position: absolute;
}
.imgNoNote {
    width: 112px;
    height: 80px;
    background-image: "{{ $r('images.noNote') }}";
    background-size: 100%;
}
.line {
    width: 100%;
    height: 0.6px;
    position: absolute;
    bottom: 1px;
}
.lineBg {
    background-color: #33000000;
}
.colorTips{
    color: #E6000000;
}  
.colorTime{
    color: #42000000;
}  
.colorNote{
    color: #8A000000;
} 
.btn {
    width: 160px;
    height: 32px;
    font-size: 12px;
    position: absolute;
    font-weight: 500;
    bottom: 20px;
    text-color: #FFFFFF;
    left: 50%;
    transform: translateX(-50%);
}
.btnBg{
    background-color: #FFB200;
}
.marginTop2{
    margin-top: 2px;
}
@media screen and (dark-mode: true) {
        .colorTextNoteTitle {
            color: #E6FFFFFF;
        }
        .colorTextNoteContent {
            color: #8AFFFFFF;
        }
        .colorTextNoteBook {
            color: #E6FFFFFF;
        }
        .colortTextContent {
            color: #8AFFFFFF;
        }
        .colorTips{
            color: #E6FFFFFF;
        }
        .colorTime{
            color: #42FFFFFF;
        }
        .colorNote{
            color: #8AFFFFFF;
        } 
        .btnBg{
            background-color: #E5A100;
        }
        .lineBg {
            background-color: #33FFFFFF;
        }
}
</style> 
<data>
    {
        "uiData": {
            "id":"",
            "title":"",
            "isNoPermissionsOrNotebook":false,
            "isNoNotebook":false,
            "isNoNote":false,
            "cardId":"",
            "noNoteStr":"",
            "tipsStr":"",
            "checkStr":"",
            "noteStr":"",
            "isShowAdd":false,
            "addStyle":"",
            "isOneData":false,
            "noteList":[  
                {
                    "id":"",
                    "title": "",
                    "showTitle":false,
                    "content":"",
                    "img":"",
                    "time":"",
                    "isShowLine":false,
                    "isShowContent":false,
                    "isShowContentNoTitle":false,
                    "isShowImg":false,
                    "contentStyle":"",
                    "titleStyle":"",
                }
            ]
        },
        
        "uiEvent": {
            "goToNotebookClick": {
                "type": "deeplink",
                "uri": "nativeapp://com.oplus.note.action.OPEN_COLLECTION_NOTE",
                "params": {
                    "from_package":"seedingCard",
                    "note_folder":"{{id}}",
                    "cardType":"specifyNotebook2x4"
                }
            },
            "selectNotebookClick": {
                "type": "deeplink",
                "uri": "nativeapp://com.oplus.note.action.card.note.selectNotebook2x4",
                "params": {
                    "cardId":"{{cardId}}",
                    "cardSize":"2x4",
                    "cardName":"seedingCard"
                }
            },
            "addNoteClick": {
                "type": "deeplink",
                "uri": "nativeapp://action.nearme.note.textnote",
                "params": {
                    "view":"false",
                    "note_folder_guid":"{{id}}",
                    "note_folder":"{{title}}",
                    "open_type":"1",
                    "from_inside":"1",
                    "cardType":"specifyNotebook2x4"
                }
            },
            "permissionsClick": {
                "type": "deeplink",
                "uri": "nativeapp://action.nearme.note.home",
                "params": {}
            },
            "itemClick": {
                "type": "deeplink",
                "uri": "nativeapp://action.nearme.note.textnote",
                "params": {
                    "guid":"{{value.id}}",
                    "view":true,
                    "open_type":1,
                    "from_inside":"1",
                    "cardType":"specifyNotebook2x4"
                }
            },
            "oneDataClick": {
                "type": "deeplink",
                "uri": "nativeapp://action.nearme.note.textnote",
                "params": {
                    "guid":"{{noteList[0].id}}",
                    "view":true,
                    "open_type":1,
                    "cardType":"specifyNotebook2x4"
                }
            },
        }
    }
</data>