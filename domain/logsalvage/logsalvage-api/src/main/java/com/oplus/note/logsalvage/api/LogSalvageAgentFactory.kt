/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - LogSalvageAgentFactory.kt
** Description:
** Version: 1.0
** Date : 2023/1/16
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2023/1/16      1.0     create file
****************************************************************/
package com.oplus.note.logsalvage.api

object LogSalvageAgentFactory {

    private var logSalvageAgent: LogSalvageAgent? = null

    fun register(logSalvageAgent: LogSalvageAgent?) {
        LogSalvageAgentFactory.logSalvageAgent = logSalvageAgent
    }

    fun getAgent(): LogSalvageAgent? {
        return logSalvageAgent
    }
}