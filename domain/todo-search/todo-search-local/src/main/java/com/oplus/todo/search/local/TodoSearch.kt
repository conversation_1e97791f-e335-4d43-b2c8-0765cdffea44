/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/25
 ** Author: niexiaokang
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ****************************************************************/
package com.oplus.todo.search.local

import android.content.Context
import com.oplus.todo.search.ITodoSearch

class TodoSearch(private val context: Context) : ITodoSearch {
    companion object {
        private const val TAG = "TodoSearch-local"
    }

    override suspend fun init() {
    }

    override suspend fun initIndexConfig(force: <PERSON><PERSON>an) {
    }

    override suspend fun notifyDataChange(force: <PERSON><PERSON><PERSON>, isFullQ<PERSON>y: <PERSON><PERSON><PERSON>) {
    }
}