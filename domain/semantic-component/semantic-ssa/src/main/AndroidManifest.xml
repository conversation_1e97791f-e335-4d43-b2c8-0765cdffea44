<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.oplus.note.semantic.ssa">

    <application>

        <!-- 待办智能解析日志在线鉴权 -->
        <meta-data
            android:name="SLP_APPKEY"
            android:value="MeATyNYIfm" />
        <meta-data
            android:name="SLP_SECRETKEY"
            android:value="NhfWSGtnWKoneKfOLDwjHPixVjogbDIN" />
        <meta-data
            android:name="SLP_CODE"
            android:value="irFwAAAFlBMQ8AAAAAAAAAADwAAAAAV95gAAAAAG+Kv2IAAAAAODE5NDljMGNmNGIzZDQ1MwAAAAAB/wD/MEUCIQCxbQuxXUQUmkCMlQzCx3edfw9x00sf6YMbagskyrvMcQIgdtkXi1rEv39222KbBv3rExDxXcW8m6nlgimcaSzGQDIAAAAuRw==" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="com.oplus.note.semantic.ssa.SemanticSsaInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>
</manifest>