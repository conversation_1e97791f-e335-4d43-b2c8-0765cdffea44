/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - PowerSaveModeWatcher.kt
 ** Description: PowerSaveModeWatcher.
 ** Version: 1.0
 ** Date : 2023/12/06
 ** Author: lihongjiang@TAG1.TAG2.TAG3.TAG4[.TAG5.TAG6.TAG7]
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ********       2023/12/06     1.0     build this module
 ****************************************************************/
package com.oplus.note.aigc.util

import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import com.oplus.note.logger.AppLogger

/**
 * 监听超级省电模式
 */
class SuperPowerSaveModeWatcher {

    companion object {
        private const val KEY_SUPER_POWER_SAVE_MODE = "super_powersave_mode_state"
        private const val TAG = "SuperPowerSaveModeWatcher"
    }

    private val watcherList: MutableList<Watcher> = ArrayList()
    private var changeCallback: () -> Unit = {}

    fun register(context: Context, onStatusChange: () -> Unit) {
        changeCallback = onStatusChange
        watcherList.add(
            Watcher(
                context,
                Settings.System.getUriFor(KEY_SUPER_POWER_SAVE_MODE),
                changeCallback
            ).apply { registerWatcher() })
    }

    fun unRegister() {
        changeCallback = {}
        for (watcher in watcherList) {
            watcher.unRegisterWatcher()
        }
        watcherList.clear()
    }

    private inner class Watcher(val context: Context, uri: Uri, changeCallback: () -> Unit) {
        private val mUri = uri
        private val observer = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean) {
                super.onChange(selfChange)
                changeCallback.invoke()
            }
        }

        fun registerWatcher() {
            kotlin.runCatching {
                context.contentResolver.registerContentObserver(mUri, true, observer)
            }.onFailure {
                AppLogger.BASIC.e(TAG, "registerWatcher error:" + it.message)
            }
        }

        fun unRegisterWatcher() {
            runCatching {
                context.contentResolver.unregisterContentObserver(observer)
            }.onFailure {
                AppLogger.BASIC.e(TAG, "unRegisterWatcher error:" + it.message)
            }
        }
    }
}