/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudBackupResponseErrorProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import java.util.List;

public class CloudBackupResponseErrorProxy {


    private CloudBackupResponseErrorProxy() {
    }


    public String getSysRecordId() {
        return "";
    }

    public void setSysRecordId(String sysRecordId) {
    }

    public List<String> getErrorOcloudIds() {
        return null;
    }

    public void setErrorOcloudIds(List<String> errorOcloudIds) {
    }

    public String getSysRecordInfo() {
        return "";
    }

    public void setSysRecordInfo(String sysRecordInfo) {
    }

    public String getCustomRecordInfo() {
        return "";
    }

    public void setCustomRecordInfo(String customRecordInfo) {

    }

    public int getSubServerErrorCode() {
        return 0;
    }

    @Override
    public String toString() {
        return "";
    }
}
