/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudStatusHelperWrapper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2022/8/16      1.0     create file
 ****************************************************************/
package com.oplus.cloud;

import android.content.Context;
import android.os.Handler;

import java.util.Map;

public class CloudStatusHelperWrapper {
    public static final String LOGIN = "key_login_state";
    public static final String SYNC_SWITCH = "key_sync_switch_state";
    public static final String NOTIFY_PATH_SPACE = "/common/space";
    public static final String NOTIFY_PATH_LOGIN = "/common/login";

    public abstract static class CloudStatusObserverWrapper {
        public CloudStatusObserverWrapper(Handler handler) {

        }

        public abstract void onPathChange(String path);
    }

    public static boolean registerCloudStatusChange(Context context, String module, CloudStatusObserverWrapper observer) {
        return false;
    }

    public static void unRegisterCloudStatusChange(Context context, CloudStatusObserverWrapper observer) {

    }

    public static Map<String, Integer> queryAll(Context context, String module) {
        return null;
    }
}
