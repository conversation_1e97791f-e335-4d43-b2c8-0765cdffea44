package com.oplus.cloud.agent;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class SyncData<T> {
    Map<String, Map<String, T>> mData = new HashMap<String, Map<String, T>>();
    public List<BaseSyncAgent.FolderBean> mFolders;
    
    private static final String DEFAULT_KEY = "the_default";
    
    private void putAddData(String key, T t) {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.ADD);
        if(map == null) {
            map = new HashMap<String, T>();
        }
        map.put(key, t);
        mData.put(SyncAgentContants.OperationType.ADD, map);
    }
    
    private T getAddData(String key) {
        if(mData.get(SyncAgentContants.OperationType.ADD) != null) {
            return mData.get(SyncAgentContants.OperationType.ADD).get(key);
        }
        return null;
    }
    
    private void putUpdateData(String key, T t) {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.UPDATE);
        if(map == null) {
            map = new HashMap<String, T>();
        }
        map.put(key, t);
        mData.put(SyncAgentContants.OperationType.UPDATE, map);
    }
    
    private T getUpdateData(String key) {
        if(mData.get(SyncAgentContants.OperationType.UPDATE) != null) {
            return mData.get(SyncAgentContants.OperationType.UPDATE).get(key);
        }
        return null;
    }
    
    private void putDeletedData(String key, T t) {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.DELETE);
        if(map == null) {
            map = new HashMap<String, T>();
        }
        map.put(key, t);
        mData.put(SyncAgentContants.OperationType.DELETE, map);
    }
    
    private T getDeletedData(String key) {
        if(mData.get(SyncAgentContants.OperationType.DELETE) != null) {
            return mData.get(SyncAgentContants.OperationType.DELETE).get(key);
        }
        return null;
    }
    
    public void putAddData(T t) {
        putAddData(DEFAULT_KEY, t);
    }
    
    public T getAddData() {
        return getAddData(DEFAULT_KEY);
    }
    
    public void putUpdateData(T t) {
        putUpdateData(DEFAULT_KEY, t);
    }
    
    public T getUpdateData() {
        return getUpdateData(DEFAULT_KEY);
    }
    
    public void putDeletedData(T t) {
        putDeletedData(DEFAULT_KEY, t);
    }
    
    public T getDeletedData() {
        return getDeletedData(DEFAULT_KEY);
    }
    
    @SuppressWarnings("unused")
    private Set<String> getAddKeySet() {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.ADD);
        if(map != null) {
            return map.keySet();
        }
        return null;
    }
    
    @SuppressWarnings("unused")
    private Set<String> getUpdateKeySet() {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.UPDATE);
        if(map != null) {
            return map.keySet();
        }
        return null;
    }
    
    @SuppressWarnings("unused")
    private Set<String> getDelKeySet() {
        HashMap<String, T> map = (HashMap<String, T>) mData.get(SyncAgentContants.OperationType.DELETE);
        if(map != null) {
            return map.keySet();
        }
        return null;
    }
}
