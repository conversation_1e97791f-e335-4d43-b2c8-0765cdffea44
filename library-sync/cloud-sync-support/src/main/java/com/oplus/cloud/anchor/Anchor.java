package com.oplus.cloud.anchor;

import android.os.Parcel;
import android.os.Parcelable;

public class Anchor implements Parcelable {
    //-1锚点为单机场景的正常锚点
    public static final long SINGLE_DEVICE_ANCHOR = -1;

    private long id;
    private String module;
    private String syncType;
    private long timestamp;

    public Anchor() {
    }

    protected Anchor(Parcel in) {
        id = in.readLong();
        module = in.readString();
        syncType = in.readString();
        timestamp = in.readLong();
    }

    public static final Creator<Anchor> CREATOR = new Creator<Anchor>() {
        @Override
        public Anchor createFromParcel(Parcel in) {
            return new Anchor(in);
        }

        @Override
        public Anchor[] newArray(int size) {
            return new Anchor[size];
        }
    };

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getSyncType() {
        return syncType;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel arg0, int arg1) {
        arg0.writeLong(id);
        arg0.writeString(module);
        arg0.writeString(syncType);
        arg0.writeLong(timestamp);
    }

    @Override
    public String toString() {
        return "Anchor{" +
                "id=" + id +
                ", module='" + module + '\'' +
                ", syncType='" + syncType + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
