# WebView 位置异常修复验证

## 问题描述
在 APK inspect 中发现 WebView 位置异常：
- **位置**: `at (-9, 472)` - X 坐标为负数，WebView 被推到屏幕外
- **尺寸**: `size 1260 × 1978` - 尺寸正常
- **URL**: `appassets.androidplatform.net/assets/tiptapcoverpaint/index.html`

## 根本原因
`WindowInsetsUtil.getDefaultDensity()` 方法在异常情况下返回 -1，导致：
1. `defaultDensity = -1.0f / 160.0f = -0.00625f` (负数)
2. `webViewWidth * defaultDensity` 得到负数
3. WebView 宽度变成负数，布局异常

## 修复方案
在以下文件中添加了密度计算的保护性检查：

### 1. WVRichEditorCoverPaint.kt
```kotlin
private val defaultDensity: Float by lazy {
    val density = WindowInsetsUtil.getDefaultDensity().toFloat()
    val baseline = CSSPropertyManagerCoverPaint.BASELINE_DENSITY.toFloat()
    val result = if (density > 0 && baseline > 0) {
        density / baseline
    } else {
        // 如果获取密度失败，使用默认值 1.0f
        1.0f
    }
    AppLogger.BASIC.d(TAG, "defaultDensity calculated: density=$density, baseline=$baseline, result=$result")
    result
}
```

### 2. CSSPropertyManagerCoverPaint.kt
添加了相同的保护性检查

### 3. CSSPropertyManager.kt
添加了相同的保护性检查

## 验证方法

### 1. 日志验证
在修复后的版本中，查看日志输出：
```
WVRichEditorCoverPaint: defaultDensity calculated: density=xxx, baseline=160.0, result=xxx
```

### 2. APK Inspect 验证
重新构建 APK 后，使用 Chrome DevTools 检查：
- WebView 位置应该是正数坐标
- WebView 应该在屏幕可见区域内

### 3. 功能验证
- WebView 内容应该正常显示
- 截图功能应该正常工作
- 触摸交互应该正常

## 预期结果
修复后，WebView 应该：
- 位置坐标为正数
- 正确显示在屏幕内
- 功能完全正常

## 测试用例
创建了 `WVRichEditorCoverPaintTest.kt` 来验证：
- 正常密度值的计算
- 负数密度值的处理
- 零密度值的处理
- WebView 布局参数的正确性
