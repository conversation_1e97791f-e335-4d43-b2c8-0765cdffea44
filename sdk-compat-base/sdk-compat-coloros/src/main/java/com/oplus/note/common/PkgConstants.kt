package com.oplus.note.common

/**
 * <AUTHOR>
 * demandId : ALM-1749654
 * description : 收集引用包名或者action相关的字段
 * updateTime : 2021/7/6
 */
open class PkgConstants {
    companion object {
        const val CLOUD_ACTION_MODULE_SWITCH_STATE_CHANGED = "oppo.cloud.action.SET_MODULE_SWITCH_STATE"

        const val SKIN_DOWN_URL = "https://note-skin.coloros.com/enum/v1/child"

        const val ACCOUNT_TYPE = "coloros"
        const val ACCOUNT_NAME = "oppo"
    }
}